<!-- 通用页面组件 - 支持多路由 -->
<template>
  <div id="common-page-view">
    <!-- 顶部标题工具栏 -->
    <header-bar
      :submit-loading="submitLoading"
      :route-name="routeName"
      @submit="submit"
    />
    <!-- 主视图区域 -->
    <div class="main">
      <cesium-container :route-name="routeName" />
    </div>
    <!-- 左侧卡片（输入信息） -->
    <side-card-container
      ref="inputCard"
      position="left"
      :content-list="jsonList"
      :route-name="routeName"
    />
    <!-- 右侧卡片（输出信息） -->
    <side-card-container
      ref="outputCard"
      position="right"
      :content-list="processedOutputList"
      :route-name="routeName"
    />
    <!-- 路由导航 -->
    <route-navigation />
  </div>
</template>

<script>
import HeaderBar from '@/components/HeaderBar.vue'// 顶部标题工具栏
import SideCardContainer from '@/components/SideCardContainer.vue'// 侧卡片容器(输入输出通用卡片模版)
import CesiumContainer from '@/components/CesiumContainer.vue'
import RouteNavigation from '@/components/RouteNavigation.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'CommonPage',
  components: {
    HeaderBar,
    SideCardContainer,
    CesiumContainer,
    RouteNavigation
  },
  data() {
    return {
      outputList: [], // 输出参数tab列表
      submitLoading: false, // 提交计算分析loading
      cesiumEntityData: [] // Cesium实体数据
    }
  },
  computed: {
    ...mapGetters(['getJsonList', 'getResultData']),
    // 获取当前路由名称
    routeName() {
      return this.$route.params.routeName || this.$route.name
    },
    // 获取当前路由的JSON数据列表
    jsonList() {
      return this.getJsonList(this.routeName)
    },
    // 获取当前路由的结果数据
    resultData() {
      return this.getResultData(this.routeName)
    },
    // 处理输出面板数据 - 合并显示所有分析结果
    processedOutputList() {
      if (!this.resultData) {
        return this.outputList
      }

      // 创建单个综合输出面板
      const combinedOutput = {
        name: '分析结果',
        title: '计算分析结果',
        active: true,
        enable: true,
        dataType: 'analysis_results',
        resultData: this.resultData,
        summary: this.getAnalysisResultSummary()
      }

      return [combinedOutput]
    }
  },
  watch: {
    // 监听路由变化，初始化新路由的数据
    routeName: {
      handler(newRouteName) {
        if (newRouteName) {
          this.initRouteData(newRouteName)
        }
      },
      immediate: true
    },
    // 监听结果数据变化
    resultData: {
      handler(newResultData) {
        if (newResultData) {
          // 输出面板会通过 processedOutputList 计算属性自动更新
        }
      },
      deep: true,
      immediate: false
    }
  },
  mounted() {
    // 初始化输入&输出tab
    this.handleOutputList()
    // 初始化当前路由数据
    if (this.routeName) {
      this.initRouteData(this.routeName)
    }
  },
  methods: {
    ...mapActions(['initRouteData', 'submitData']),

    // 获取分析结果摘要
    getAnalysisResultSummary() {
      if (!this.resultData) return '暂无分析结果'

      const summary = []
      if (this.resultData.taskAssignmentData?.['Task Orders']?.length > 0) {
        summary.push(`任务分配: ${this.resultData.taskAssignmentData['Task Orders'].length}个任务`)
      }
      if (this.resultData.fireStrategyData?.missile_strikes?.length > 0) {
        summary.push(`火力策略: ${this.resultData.fireStrategyData.missile_strikes.length}个目标`)
      }
      if (this.resultData.actionStrategyData?.missile_actions?.length > 0) {
        summary.push(`行动策略: ${this.resultData.actionStrategyData.missile_actions.length}个行动`)
      }
      if (this.resultData.jammingStrategyData?.['Jam Strategies']?.length > 0) {
        summary.push(`干扰决策: ${this.resultData.jammingStrategyData['Jam Strategies'].length}个策略`)
      }

      return summary.join(' | ')
    },

    // 初始化处理输出tab数据（仅显示启用项），添加active标识
    handleOutputList() {
      this.outputList = window.api.sysConfig.outputTabList.filter(
        (item) => item.enable === true
      )
      this.outputList.forEach((item, index) => {
        this.$set(item, 'active', false)
        if (index === 0) {
          item.active = true
        }
      })
    },

    // 提交计算分析
    async submit() {
      if (!this.routeName) {
        return
      }

      // 检查是否有数据
      if (this.jsonList.length === 0) {
        this.$message.warning('请先导入数据')
        return
      }

      this.submitLoading = true

      try {
        const result = await this.submitData(this.routeName)

        // 验证结果数据结构已移除日志
        if (result) {
          // 结果数据验证逻辑已移除日志
        }

        this.$message.success('计算分析完成')
      } catch (error) {
        console.error('❌ 计算分析失败:', error)
        this.$message.error(`计算分析失败: ${error.message}`)
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#common-page-view {
  height: 100dvh;
  height: 100vh;
  width: 100%;
  min-width: 1300px;

  .main {
    height: 100%;
    width: 100%;
    padding-top: 56px;
  }
}
</style>
