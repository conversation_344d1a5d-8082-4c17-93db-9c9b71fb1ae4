<!-- 顶部标题工具栏 -->
<!-- 按钮名称都待定 -->
<template>
  <div class="header-bar">
    <div class="left-box">
      <el-button type="text" size="medium" @click="triggerFileInput">导入数据</el-button>
      <input
        ref="fileInput"
        type="file"
        accept=".json"
        style="display: none"
        @change="handleFileImport"
      >
      <el-divider direction="vertical" />
      <!-- <el-button type="text" size="medium">导出数据</el-button> -->
      <!-- <el-divider direction="vertical" />
      <el-button type="text" size="medium" @click="$emit('inputToCesium')">提交数据</el-button> -->
    </div>
    <div class="center-box">
      <div class="title">{{ currentRouteName }}</div>
    </div>
    <div class="right-box">
      <el-button type="text" size="medium" :loading="submitLoading" @click="$emit('submit')">计算分析</el-button>
      <el-divider direction="vertical" />
      <el-button type="text" size="medium">保存分析结果</el-button>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'HeaderBar',
  props: {
    // 提交计算分析loading
    submitLoading: {
      type: Boolean,
      default: false
    },
    // 路由名称
    routeName: {
      type: String,
      required: true
    }
  },
  emits: ['submit'],
  data() {
    return {
      title: window.api.sysConfig.modelName
    }
  },
  computed: {
    // 获取当前路由的名称
    currentRouteName() {
      return this.$route.name || this.title
    }
  },
  methods: {
    ...mapActions(['addJsonData']),
    // 触发文件选择
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    // 处理文件导入
    handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target.result)

          // 验证数据格式
          if (this.validateJsonData(jsonData)) {
            const dataToStore = {
              name: jsonData.title || file.name, // 显示名称
              title: jsonData.title || file.name, // 标题
              fileName: file.name,
              importTime: new Date().toISOString(),
              value: jsonData // 原始JSON数据存储在value字段中
            }

            // 直接存入Vuex，传递路由名称
            this.addJsonData({
              routeName: this.routeName,
              jsonData: dataToStore
            })

            this.$message.success(`成功导入文件: ${file.name}`)
          } else {
            this.$message.error('文件格式不正确，请检查JSON数据结构')
          }
        } catch (error) {
          this.$message.error('文件解析失败，请确保是有效的JSON文件')
        }
      }
      reader.readAsText(file)
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    },
    // 验证JSON数据格式
    validateJsonData(data) {
      return data && typeof data === 'object'
    }
  }
}
</script>

<style lang="scss" scoped>
.header-bar {
    width: 100%;
    height: 80px;
    min-width: 1300px;
    position: fixed;
    top: 0;
    left: 0;
    background-image: url('@/assets/images/top_bg.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 20px 30px;
    z-index: 1;

    .left-box,
    .right-box,
    .center-box {
        width: 30%;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        padding: 0 8px;
        gap: 4px;
    }

    .center-box {
        justify-content: center;
        font-size: 26px;
        font-weight: 600;
        color: #fff;
    }

    .left-box {
        justify-content: flex-start;
    }

    .right-box {
        justify-content: flex-end;
    }
    .el-divider{
background: #3877F2;
height: 10px;
    }
    .el-button--text{
        color: #d3edff;
        background: url('@/assets/images/header_btn_bg.png');
            background-size: 100% 3px;
            background-position: bottom;
            background-repeat: no-repeat;
        &:hover{
           color: #fff;
        }
    }
}
</style>
