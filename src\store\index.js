/*
 * @Description: 状态管理
 * @Author: tzs
 * @Date: 2025-07-07 15:42:38
 * @LastEditors: tzs
 * @LastEditTime: 2025-07-08 10:16:22
 *
 */
import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

// 加载JSON文件的辅助函数
async function loadJsonFile(filePath) {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }

}

const store = new Vuex.Store({
  state: {
    // 按路由名称存储数据，每个路由有独立的jsonList和resultData
    routeData: {},
  },

  mutations: {
    // 初始化路由数据
    INIT_ROUTE_DATA(state, routeName) {
      if (!state.routeData[routeName]) {
        Vue.set(state.routeData, routeName, {
          jsonList: [],
          resultData: null,
        });
      }
    },
    // 设置指定路由的JSON数据列表
    SET_JSON_LIST(state, { routeName, jsonList }) {
      if (!state.routeData[routeName]) {
        Vue.set(state.routeData, routeName, { jsonList: [], resultData: null });
      }
      state.routeData[routeName].jsonList = jsonList;
    },
    // 添加JSON数据到指定路由
    ADD_JSON_DATA(state, { routeName, jsonData }) {
      if (!state.routeData[routeName]) {
        Vue.set(state.routeData, routeName, { jsonList: [], resultData: null });
      }
      // 将所有现有数据设为非激活状态
      state.routeData[routeName].jsonList.forEach((item) => {
        item.active = false;
      });
      // 新添加的数据设为激活状态
      jsonData.active = true;
      state.routeData[routeName].jsonList.push(jsonData);
    },
    // 设置指定路由的激活JSON数据
    SET_ACTIVE_JSON_DATA(state, { routeName, activeIndex }) {
      if (state.routeData[routeName] && state.routeData[routeName].jsonList) {
        // 将所有数据设为非激活状态
        state.routeData[routeName].jsonList.forEach((item, index) => {
          item.active = index === activeIndex;
        });
      }
    },
    // 删除指定路由的JSON数据
    REMOVE_JSON_DATA(state, { routeName, index }) {
      if (
        state.routeData[routeName] &&
        index >= 0 &&
        index < state.routeData[routeName].jsonList.length
      ) {
        state.routeData[routeName].jsonList.splice(index, 1);
      }
    },
    // 清空指定路由的JSON数据列表
    CLEAR_JSON_LIST(state, routeName) {
      if (state.routeData[routeName]) {
        state.routeData[routeName].jsonList = [];
      }
    },
    // 设置指定路由的计算结果数据
    SET_RESULT_DATA(state, { routeName, resultData }) {
      if (!state.routeData[routeName]) {
        Vue.set(state.routeData, routeName, { jsonList: [], resultData: null });
      }
      state.routeData[routeName].resultData = resultData;
    },
    // 清空指定路由的计算结果数据
    CLEAR_RESULT_DATA(state, routeName) {
      if (state.routeData[routeName]) {
        state.routeData[routeName].resultData = null;
      }
    },
    // 设置指定路由激活的JSON数据tab
    SET_ACTIVE_JSON_TAB(state, { routeName, index }) {
      if (state.routeData[routeName]) {
        state.routeData[routeName].jsonList.forEach((item, i) => {
          item.active = i === index;
        });
      }
    },
  },
  actions: {
    // 初始化路由数据
    initRouteData({ commit }, routeName) {
      commit("INIT_ROUTE_DATA", routeName);
    },
    // 添加JSON数据
    addJsonData({ commit }, { routeName, jsonData }) {
      commit("ADD_JSON_DATA", { routeName, jsonData });
    },
    // 设置激活的JSON数据
    setActiveJsonData({ commit }, { routeName, activeIndex }) {
      commit("SET_ACTIVE_JSON_DATA", { routeName, activeIndex });
    },
    // 设置JSON数据列表
    setJsonList({ commit }, { routeName, jsonList }) {
      commit("SET_JSON_LIST", { routeName, jsonList });
    },
    // 删除JSON数据
    removeJsonData({ commit }, { routeName, index }) {
      commit("REMOVE_JSON_DATA", { routeName, index });
    },
    // 清空JSON数据列表
    clearJsonList({ commit }, routeName) {
      commit("CLEAR_JSON_LIST", routeName);
    },
    // 设置计算结果数据
    setResultData({ commit }, { routeName, resultData }) {
      commit("SET_RESULT_DATA", { routeName, resultData });
    },
    // 清空计算结果数据
    clearResultData({ commit }, routeName) {
      commit("CLEAR_RESULT_DATA", routeName);
    },
    // 提交数据进行计算分析
    async submitData({ commit, state }, routeName) {
      if (!state.routeData[routeName]) {
        return null;
      }

      // 检查是否有输入数据
      if (
        !state.routeData[routeName].jsonList ||
        state.routeData[routeName].jsonList.length === 0
      ) {
        throw new Error("没有可分析的数据");
      }

      // 获取当前路由下的所有输入数据
      const allInputData = state.routeData[routeName].jsonList;

      try {
        // 首先尝试调用真实API
        const { submitInputData } = require("@/api");
        const requestData = {
          routeName: routeName,
          inputData: allInputData, // 传递所有tab数据
          activeTabIndex: allInputData.findIndex((item) => item.active), // 标记当前激活的tab索引
          timestamp: new Date().toISOString(),
        };
        const result = await submitInputData(requestData);
        commit("SET_RESULT_DATA", { routeName, resultData: result });
        return result;
      } catch (error) {
        // API调用失败时，从静态JSON文件加载数据
        try {
          // 模拟网络延迟
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // 并行加载五个JSON文件，包括新增的资源需求分析
          const [
            taskAssignmentData,
            fireStrategyData,
            actionStrategyData,
            jammingStrategyData,
            resourceRequirementData,
          ] = await Promise.all([
            loadJsonFile("/mock-data/任务分配.json"),
            loadJsonFile("/mock-data/火力策略.json"),
            loadJsonFile("/mock-data/行动策略.json"),
            loadJsonFile("/mock-data/干扰决策.json"),
            loadJsonFile("/mock-data/资源需求分析.json"),
          ]);

          // 构建分析结果
          const analysisResult = {
            analysisId: `ANALYSIS_${Date.now()}`,
            timestamp: new Date().toISOString(),
            routeName: routeName,
            status: "success",
            message: "计算分析完成",
            inputDataCount: 1,
            dataSource: "static_files",
            taskAssignmentData: taskAssignmentData || {},
            fireStrategyData: fireStrategyData || {},
            actionStrategyData: actionStrategyData || {},
            jammingStrategyData: jammingStrategyData || {},
            resourceRequirementData: resourceRequirementData || {},
          };

          commit("SET_RESULT_DATA", { routeName, resultData: analysisResult });
          return analysisResult;
        } catch (fileError) {
          // 错误处理保持，但移除console
        }
      }
    },
    // 设置激活的JSON数据tab
    setActiveJsonTab({ commit }, { routeName, index }) {
      commit("SET_ACTIVE_JSON_TAB", { routeName, index });
    },
  },

  getters: {
    // 获取指定路由的JSON数据列表
    getJsonList: (state) => (routeName) => {
      return state.routeData[routeName]
        ? state.routeData[routeName].jsonList
        : [];
    },
    // 获取指定路由的结果数据
    getResultData: (state) => (routeName) => {
      return state.routeData[routeName]
        ? state.routeData[routeName].resultData
        : null;
    },
    // 获取指定路由的JSON数据列表长度
    getJsonListLength: (state) => (routeName) => {
      return state.routeData[routeName]
        ? state.routeData[routeName].jsonList.length
        : 0;
    },
    // 检查指定路由是否有JSON数据
    hasJsonData: (state) => (routeName) => {
      return state.routeData[routeName]
        ? state.routeData[routeName].jsonList.length > 0
        : false;
    },
    // 检查指定路由是否有结果数据
    hasResultData: (state) => (routeName) => {
      return state.routeData[routeName]
        ? state.routeData[routeName].resultData !== null
        : false;
    },
  },
});

export default store;
