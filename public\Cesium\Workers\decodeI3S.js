/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.113
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as $}from"./chunk-HBNGVONA.js";import{a as Z}from"./chunk-54KKWN6C.js";import{a as Q}from"./chunk-TDXQUZ7V.js";import{a as Y,b as D,d as W,e as X}from"./chunk-T4TQMW7B.js";import{a as N}from"./chunk-7TT2TZHW.js";import"./chunk-XQTMMRVI.js";import"./chunk-QHDGFGBI.js";import{d as K,e as f}from"./chunk-MVZBAA6W.js";var H=K($(),1),q;function tt(r,t,e,n,c,s){let o=e*(1-r)+n*r,u=c*(1-r)+s*r;return o*(1-t)+u*t}function P(r,t,e,n){let c=r+t*e;return n[c]}function et(r,t,e){let n=e.nativeExtent,c=(r-n.west)/(n.east-n.west)*(e.width-1),s=(t-n.south)/(n.north-n.south)*(e.height-1),o=Math.floor(c),u=Math.floor(s);c-=o,s-=u;let y=o<e.width?o+1:o,a=u<e.height?u+1:u;u=e.height-1-u,a=e.height-1-a;let l=P(o,u,e.width,e.buffer),i=P(y,u,e.width,e.buffer),w=P(o,a,e.width,e.buffer),A=P(y,a,e.width,e.buffer),p=tt(c,s,l,i,w,A);return p=p*e.scale+e.offset,p}function J(r,t,e){for(let n=0;n<e.length;n++){let c=e[n].nativeExtent,s=new Y;if(e[n].projectionType==="WebMercator"){let o=e[n].projection._ellipsoid._radii;s=new Q(new W(o.x,o.y,o.z)).project(new D(r,t,0))}else s.x=r,s.y=t;if(s.x>c.west&&s.x<c.east&&s.y>c.south&&s.y<c.north)return et(s.x,s.y,e[n])}return 0}function nt(r,t,e,n,c,s,o){if(o)return;let u=J(c.longitude,c.latitude,s);for(let y=0;y<r;++y){let a=J(c.longitude+N.toRadians(e*t[y*3]),c.latitude+N.toRadians(n*t[y*3+1]),s);t[y*3+2]+=a-u}}function rt(r,t,e,n,c,s,o,u,y){if(r===0||!f(t)||t.length===0)return;let a=new W(Math.sqrt(o.x),Math.sqrt(o.y),Math.sqrt(o.z));for(let l=0;l<r;++l){let i=l*3,w=i+1,A=i+2,p=new D;p.longitude=n.longitude+N.toRadians(u*t[i]),p.latitude=n.latitude+N.toRadians(y*t[w]),p.height=n.height+t[A];let h={};a.cartographicToCartesian(p,h),h.x-=c.x,h.y-=c.y,h.z-=c.z;let m={};if(X.multiplyByVector(s,h,m),t[i]=m.x,t[w]=m.y,t[A]=m.z,f(e)){let C=new Y(e[i],e[w],e[A]),x={};X.multiplyByVector(s,C,x),e[i]=x.x,e[w]=x.y,e[A]=x.z}}}function ot(r,t,e){for(let n=0;n<r;++n){let c=e[n*4]/65535,s=e[n*4+1]/65535,o=(e[n*4+2]-e[n*4])/65535,u=(e[n*4+3]-e[n*4+1])/65535;t[n*2]*=o,t[n*2]+=c,t[n*2+1]*=u,t[n*2+1]+=s}}function st(r,t,e,n,c,s){if(r===0||!f(e)||e.length===0)return{buffers:[],bufferViews:[],accessors:[],meshes:[],nodes:[],nodesInScene:[]};let o=[],u=[],y=[],a=[],l=[],i=[];f(t)&&(r=t.length);let w=new Uint32Array(r);if(f(t))for(let b=0;b<r;++b)w[b]=t[b];else for(let b=0;b<r;++b)w[b]=b;let A=new Blob([w],{type:"application/binary"}),p=URL.createObjectURL(A),h=r,m=e.subarray(0,h*3),C=new Blob([m],{type:"application/binary"}),x=URL.createObjectURL(C),I=Number.POSITIVE_INFINITY,g=Number.NEGATIVE_INFINITY,O=Number.POSITIVE_INFINITY,L=Number.NEGATIVE_INFINITY,U=Number.POSITIVE_INFINITY,j=Number.NEGATIVE_INFINITY;for(let b=0;b<m.length/3;b++)I=Math.min(I,m[b*3+0]),g=Math.max(g,m[b*3+0]),O=Math.min(O,m[b*3+1]),L=Math.max(L,m[b*3+1]),U=Math.min(U,m[b*3+2]),j=Math.max(j,m[b*3+2]);let R=n?n.subarray(0,h*3):void 0,S;if(f(R)){let b=new Blob([R],{type:"application/binary"});S=URL.createObjectURL(b)}let _=c?c.subarray(0,h*2):void 0,k;if(f(_)){let b=new Blob([_],{type:"application/binary"});k=URL.createObjectURL(b)}let T=f(s)?s.subarray(0,h*4):void 0,z;if(f(T)){let b=new Blob([T],{type:"application/binary"});z=URL.createObjectURL(b)}let E=0,G=0,B=0,F=0,M=0,d=E,V={};return V.POSITION=E,o.push({uri:x,byteLength:m.byteLength}),u.push({buffer:E,byteOffset:0,byteLength:m.byteLength,target:34962}),y.push({bufferView:E,byteOffset:0,componentType:5126,count:r,type:"VEC3",max:[I,O,U],min:[g,L,j]}),f(S)&&(++d,G=d,V.NORMAL=G,o.push({uri:S,byteLength:R.byteLength}),u.push({buffer:G,byteOffset:0,byteLength:R.byteLength,target:34962}),y.push({bufferView:G,byteOffset:0,componentType:5126,count:r,type:"VEC3"})),f(k)&&(++d,B=d,V.TEXCOORD_0=B,o.push({uri:k,byteLength:_.byteLength}),u.push({buffer:B,byteOffset:0,byteLength:_.byteLength,target:34962}),y.push({bufferView:B,byteOffset:0,componentType:5126,count:r,type:"VEC2"})),f(z)&&(++d,F=d,V.COLOR_0=F,o.push({uri:z,byteLength:T.byteLength}),u.push({buffer:F,byteOffset:0,byteLength:T.byteLength,target:34962}),y.push({bufferView:F,byteOffset:0,componentType:5121,normalized:!0,count:r,type:"VEC4"})),++d,M=d,o.push({uri:p,byteLength:w.byteLength}),u.push({buffer:M,byteOffset:0,byteLength:w.byteLength,target:34963}),y.push({bufferView:M,byteOffset:0,componentType:5125,count:r,type:"SCALAR"}),a.push({primitives:[{attributes:V,indices:M,material:0}]}),i.push(0),l.push({mesh:0}),{buffers:o,bufferViews:u,accessors:y,meshes:a,nodes:l,nodesInScene:i}}function it(r,t,e,n){let c=new Uint8Array(r,0,5);return c[0]===68&&c[1]===82&&c[2]===65&&c[3]===67&&c[4]===79?ct(r,e):at(r,t,e,n)}function ct(r){let t=q,e=new t.DecoderBuffer,n=new Uint8Array(r);e.Init(n,n.length);let c=new t.Decoder,s=c.GetEncodedGeometryType(e),o=new t.MetadataQuerier,u,y;s===t.TRIANGULAR_MESH&&(u=new t.Mesh,y=c.DecodeBufferToMesh(e,u));let a={vertexCount:[0],featureCount:0};if(f(y)&&y.ok()&&u.ptr!==0){let l=u.num_faces(),i=u.num_attributes(),w=u.num_points();a.indices=new Uint32Array(l*3);let A=a.indices;a.vertexCount[0]=w,a.scale_x=1,a.scale_y=1;let p=new t.DracoInt32Array(3);for(let h=0;h<l;++h)c.GetFaceFromMesh(u,h,p),A[h*3]=p.GetValue(0),A[h*3+1]=p.GetValue(1),A[h*3+2]=p.GetValue(2);t.destroy(p);for(let h=0;h<i;++h){let m=c.GetAttribute(u,h),C=ut(t,c,u,m,w),x=m.attribute_type(),I="unknown";x===t.POSITION?I="positions":x===t.NORMAL?I="normals":x===t.COLOR?I="colors":x===t.TEX_COORD&&(I="uv0s");let g=c.GetAttributeMetadata(u,h);if(g.ptr!==0){let O=o.NumEntries(g);for(let L=0;L<O;++L){let U=o.GetEntryName(g,L);U==="i3s-scale_x"?a.scale_x=o.GetDoubleEntry(g,"i3s-scale_x"):U==="i3s-scale_y"?a.scale_y=o.GetDoubleEntry(g,"i3s-scale_y"):U==="i3s-attribute-type"&&(I=o.GetStringEntry(g,"i3s-attribute-type"))}}f(a[I])&&console.log("Attribute already exists",I),a[I]=C,I==="feature-index"&&a.featureCount++}t.destroy(u)}return t.destroy(o),t.destroy(c),a}function ut(r,t,e,n,c){let s=n.num_components()*c,o,y=[function(){},function(){o=new r.DracoInt8Array(s),t.GetAttributeInt8ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Int8Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){o=new r.DracoInt8Array(s),t.GetAttributeUInt8ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Uint8Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){o=new r.DracoInt16Array(s),t.GetAttributeInt16ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Int16Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){o=new r.DracoInt16Array(s),t.GetAttributeUInt16ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Uint16Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){o=new r.DracoInt32Array(s),t.GetAttributeInt32ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Int32Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){o=new r.DracoInt32Array(s),t.GetAttributeUInt32ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Uint32Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){},function(){},function(){o=new r.DracoFloat32Array(s),t.GetAttributeFloatForAllPoints(e,n,o)||console.error("Bad stream");let l=new Float32Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l},function(){},function(){o=new r.DracoUInt8Array(s),t.GetAttributeUInt8ForAllPoints(e,n,o)||console.error("Bad stream");let l=new Uint8Array(s);for(let i=0;i<s;++i)l[i]=o.GetValue(i);return l}][n.data_type()]();return f(o)&&r.destroy(o),y}var v={position:function(r,t,e){let n=r.vertexCount*3;return r.positions=new Float32Array(t,e,n),e+=n*4,e},normal:function(r,t,e){let n=r.vertexCount*3;return r.normals=new Float32Array(t,e,n),e+=n*4,e},uv0:function(r,t,e){let n=r.vertexCount*2;return r.uv0s=new Float32Array(t,e,n),e+=n*4,e},color:function(r,t,e){let n=r.vertexCount*4;return r.colors=new Uint8Array(t,e,n),e+=n,e},featureId:function(r,t,e){let n=r.featureCount;return e+=n*8,e},id:function(r,t,e){let n=r.featureCount;return e+=n*8,e},faceRange:function(r,t,e){let n=r.featureCount*2;return r.faceRange=new Uint32Array(t,e,n),e+=n*4,e},uvRegion:function(r,t,e){let n=r.vertexCount*4;return r["uv-region"]=new Uint16Array(t,e,n),e+=n*2,e},region:function(r,t,e){let n=r.vertexCount*4;return r["uv-region"]=new Uint16Array(t,e,n),e+=n*2,e}};function at(r,t,e,n){let c={vertexCount:0},s=new DataView(r);try{let o=0;if(c.vertexCount=s.getUint32(o,1),o+=4,c.featureCount=s.getUint32(o,1),o+=4,f(e))for(let u=0;u<e.attributes.length;u++)f(v[e.attributes[u]])?o=v[e.attributes[u]](c,r,o):console.error("Unknown decoder for",e.attributes[u]);else{let u=t.ordering,y=t.featureAttributeOrder;f(n)&&f(n.geometryData)&&f(n.geometryData[0])&&f(n.geometryData[0].params)&&(u=Object.keys(n.geometryData[0].params.vertexAttributes),y=Object.keys(n.geometryData[0].params.featureAttributes));for(let a=0;a<u.length;a++){let l=v[u[a]];o=l(c,r,o)}for(let a=0;a<y.length;a++){let l=v[y[a]];o=l(c,r,o)}}}catch(o){console.error(o)}return c.scale_x=1,c.scale_y=1,c}function lt(r){let t=it(r.binaryData,r.schema,r.bufferInfo,r.featureData);f(r.geoidDataList)&&r.geoidDataList.length>0&&nt(t.vertexCount,t.positions,t.scale_x,t.scale_y,r.cartographicCenter,r.geoidDataList,!1),rt(t.vertexCount,t.positions,t.normals,r.cartographicCenter,r.cartesianCenter,r.parentRotation,r.ellipsoidRadiiSquare,t.scale_x,t.scale_y),f(t.uv0s)&&f(t["uv-region"])&&ot(t.vertexCount,t.uv0s,t["uv-region"]);let e=st(t.vertexCount,t.indices,t.positions,t.normals,t.uv0s,t.colors),n={};if(f(t["feature-index"]))n.positions=t.positions,n.indices=t.indices,n.featureIndex=t["feature-index"],n.cartesianCenter=r.cartesianCenter,n.parentRotation=r.parentRotation;else if(f(t.faceRange)){n.positions=t.positions,n.indices=t.indices,n.sourceURL=r.url,n.cartesianCenter=r.cartesianCenter,n.parentRotation=r.parentRotation,n.featureIndex=new Array(t.positions.length);for(let s=0;s<t.faceRange.length-1;s+=2){let o=s/2,u=t.faceRange[s],y=t.faceRange[s+1];for(let a=u;a<=y;a++)n.featureIndex[a*3]=o,n.featureIndex[a*3+1]=o,n.featureIndex[a*3+2]=o}}return e._customAttributes=n,{meshData:e}}async function ft(r,t){let e=r.webAssemblyConfig;return f(e)&&f(e.wasmBinaryFile)?q=await(0,H.default)(e):q=await(0,H.default)(),!0}function yt(r,t){let e=r.webAssemblyConfig;return f(e)?ft(r,t):lt(r,t)}var gt=Z(yt);export{gt as default};
