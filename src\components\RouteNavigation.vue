<!-- 路由导航组件 -->
<template>
  <div class="route-navigation">
    <div class="nav-container">
      <div class="nav-title">路由导航</div>
      <div class="nav-buttons">
        <el-button
          v-for="(route, index) in availableRoutes"
          :key="route.name"
          :type="currentRoute === route.name ? 'primary' : 'default'"
          size="mini"
          @click="navigateToRoute(route)"
        >
          {{ index + 1 }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RouteNavigation',
  computed: {
    // 获取当前路由名称
    currentRoute() {
      return this.$route.name
    },
    // 从路由配置中获取可用的路由列表
    availableRoutes() {
      return this.$router.options.routes.filter(route =>
        route.path && route.path.startsWith('/route/') && route.name
      )
    }
  },
  methods: {
    navigateToRoute(route) {
      if (this.currentRoute !== route.name) {
        this.$router.push({ name: route.name })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.route-navigation {
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;

  .nav-container {
    background: rgba(4, 41, 112, 0.95);
    border: 1px solid #3877F2;
    border-radius: 25px;
    padding: 8px 20px;
    box-shadow: 0 6px 20px rgba(56, 119, 242, 0.4);
    backdrop-filter: blur(15px);

    .nav-title {
      color: #A3C2FF;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 5px;
      text-align: center;
    }

    .nav-buttons {
      display: flex;
      flex-direction: row;
      gap: 12px;
      align-items: center;

      .el-button {
        width: 36px;
        height: 36px;
        padding: 0;
        font-size: 13px;
        font-weight: 600;
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &.el-button--primary {
          background: linear-gradient(135deg, #3877F2, #67c23a);
          border-color: #3877F2;
          color: #fff;
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(56, 119, 242, 0.4);
        }

        &.el-button--default {
          background: rgba(56, 119, 242, 0.1);
          border-color: rgba(56, 119, 242, 0.3);
          color: #A3C2FF;

          &:hover {
            background: rgba(56, 119, 242, 0.2);
            border-color: #3877F2;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(56, 119, 242, 0.3);
          }
        }
      }
    }
  }
}
</style>
