/**
 * Cesium地图管理器
 * 负责Cesium实例的初始化、实体管理、数据渲染等核心功能
 */

import { DataVisualizationUtils } from './dataVisualizationUtils'

export class CesiumManager {
  constructor(containerId, routeName, options = {}) {
    this.containerId = containerId
    this.routeName = routeName
    this.viewer = null
    this.dataVisualizationUtils = null

    // 状态管理
    this.currentEntityIds = new Set()
    this.typeIconMap = new Map()
    this.preGeneratedIcons = null

    // 配置选项
    this.options = {
      imageryUrl: 'http://localhost:8000/google_0-9/{z}/{x}/{y}.jpg',
      initialCamera: {
        longitude: 115,
        latitude: 40,
        height: 6000000
      },
      ...options
    }

    // 事件回调
    this.callbacks = {
      onInitialized: null,
      onError: null,
      onEntityAdded: null,
      onEntityRemoved: null
    }
  }

  /**
   * 初始化Cesium地图
   */
  async initialize() {
    try {
      const Cesium = window.Cesium
      if (!Cesium) {
        throw new Error('Cesium library not loaded')
      }

      // 设置空的访问令牌，避免Ion服务请求
      Cesium.Ion.defaultAccessToken = ''

      // 初始化Cesium地图
      this.viewer = new Cesium.Viewer(this.containerId, {
        animation: false,
        timeline: false,
        shouldAnimate: true,
        homeButton: false,
        fullscreenButton: false,
        baseLayerPicker: false,
        geocoder: false,
        navigationHelpButton: false,
        infoBox: false,
        sceneMode: Cesium.SceneMode.SCENE3D,
        sceneModePicker: false,
        selectionIndicator: false,
        imageryProvider: false
      })

      // 添加影像图层
      const imageryLayers = this.viewer.imageryLayers
      imageryLayers.addImageryProvider(
        new Cesium.UrlTemplateImageryProvider({
          url: this.options.imageryUrl,
          minimumLevel: 0,
          maximumLevel: 8
        })
      )

      // 设置初始相机视角
      this.viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(
          this.options.initialCamera.longitude,
          this.options.initialCamera.latitude,
          this.options.initialCamera.height
        )
      })

      // 初始化数据可视化工具
      this.dataVisualizationUtils = new DataVisualizationUtils(this.viewer)

      // 初始化图标配置
      this.initPreGeneratedIcons()

      // 触发初始化完成回调
      if (this.callbacks.onInitialized) {
        this.callbacks.onInitialized(this.viewer)
      }

      return this.viewer
    } catch (error) {
      
      if (this.callbacks.onError) {
        this.callbacks.onError(error)
      }
      throw error
    }
  }

  /**
   * 初始化图标配置
   */
  initPreGeneratedIcons() {
    // 导入统一的图标配置
    const iconConfig = require('./iconConfig').default;
    // 使用导弹图标配置
    this.preGeneratedIcons = iconConfig.missile;
    // 存储完整图标配置以供其他方法使用
    this.iconConfig = iconConfig;
  }

  /**
   * 设置事件回调
   */
  setCallback(eventName, callback) {
    if (Object.prototype.hasOwnProperty.call(this.callbacks, eventName)) {
      this.callbacks[eventName] = callback
    }
  }

  /**
   * 获取Cesium viewer实例
   */
  getViewer() {
    return this.viewer
  }

  /**
   * 检查是否已初始化
   */
  isInitialized() {
    return this.viewer !== null
  }

  /**
   * 渲染导弹轨迹数据
   */
  renderMissileData(missileData) {
    if (!this.isInitialized()) {
      return
    }

    if (!missileData || !Array.isArray(missileData)) {
      return
    }

    // 只清空导弹相关的实体，保留分析结果
    this.clearMissileEntities()

    const allPositions = []
    const newEntityIds = new Set()

    missileData.forEach((missile, index) => {
      const trackData = this.extractTrackData(missile)

      if (!trackData || !Array.isArray(trackData)) {
        return
      }

      // 获取导弹类型和对应的图标配置
      const typeId = missile.typeId || missile.type_id || 'DEFAULT'
      const iconConfig = this.getMissileIconConfig(typeId)

      // 批量生成位置坐标
      const positions = this.generatePositions(trackData)
      allPositions.push(...positions)

      // 渲染轨迹点和线
      this.renderMissileEntities(missile, trackData, positions, iconConfig, typeId, newEntityIds)
    })

    // 更新当前实体ID集合
    this.currentEntityIds = newEntityIds

    // 自动调整视角
    this.adjustCameraView(allPositions)

  }

  /**
   * 渲染分析结果数据
   */
  renderAnalysisResults(resultData) {
    if (!this.isInitialized()) {
      return
    }

    if (!this.dataVisualizationUtils) {
      return
    }

    if (!resultData) {
      return
    }

    // 清空现有的分析结果实体（保留原始导弹数据）
    this.clearAnalysisResultEntities()

    let renderCount = 0

    // 渲染任务分配数据
    if (this.hasValidData(resultData.taskAssignmentData, 'Task Orders')) {
      try {
        this.dataVisualizationUtils.renderTaskAssignmentData(
          resultData.taskAssignmentData['Task Orders'],
          this.routeName
        )
        renderCount++
      } catch (error) {
      }
    }

    // 渲染火力策略数据
    if (this.hasValidData(resultData.fireStrategyData, 'missile_strikes')) {
      try {
        this.dataVisualizationUtils.renderFireStrategyData(
          resultData.fireStrategyData.missile_strikes,
          this.routeName
        )
        renderCount++
      } catch (error) {
      }
    }

    // 渲染行动策略数据
    if (this.hasValidData(resultData.actionStrategyData, 'missile_actions')) {
      try {
        this.dataVisualizationUtils.renderActionStrategyData(
          resultData.actionStrategyData.missile_actions,
          this.routeName
        )
        renderCount++
      } catch (error) {
      }
    }

    // 渲染干扰决策数据
    if (this.hasValidData(resultData.jammingStrategyData, 'Jam Strategies')) {
      try {
        this.dataVisualizationUtils.renderJammingStrategyData(
          resultData.jammingStrategyData['Jam Strategies'],
          this.routeName
        )
        renderCount++
      } catch (error) {
      }
    }

    // 渲染资源需求分析数据
    if (this.hasValidData(resultData.resourceRequirementData, 'refuelingPlan')) {
      try {
        this.dataVisualizationUtils.renderResourceRequirementData(
          resultData.resourceRequirementData,
          this.routeName
        )
        renderCount++
      } catch (error) {
      }
    }

    // 分析结果渲染完成，共渲染 ${renderCount} 种数据类型
  }

  /**
   * 检查数据是否有效
   */
  hasValidData(dataContainer, fieldName) {
    return dataContainer &&
           dataContainer[fieldName] &&
           Array.isArray(dataContainer[fieldName]) &&
           dataContainer[fieldName].length > 0
  }

  /**
   * 清空当前路由的所有实体
   */
  clearRouteEntities() {
    if (!this.viewer) return

    const entitiesToRemove = []

    // 遍历所有实体，找到属于当前路由的实体
    for (let i = 0; i < this.viewer.entities.values.length; i++) {
      const entity = this.viewer.entities.values[i]
      if (entity.id && entity.id.startsWith(`${this.routeName}_`)) {
        entitiesToRemove.push(entity.id)
      }
    }

    // 删除找到的实体
    entitiesToRemove.forEach(entityId => {
      try {
        this.viewer.entities.removeById(entityId)
        if (this.callbacks.onEntityRemoved) {
          this.callbacks.onEntityRemoved(entityId)
        }
      } catch (error) {
        // 静默处理错误
      }
    })

    this.currentEntityIds.clear()
  }

  /**
   * 强制清理所有实体（用于路由切换）
   */
  clearAllEntities() {
    if (!this.viewer) return

    try {
      const entityCount = this.viewer.entities.values.length
      this.viewer.entities.removeAll()
      this.currentEntityIds.clear()
      } catch (error) {
      }
  }

  /**
   * 清空分析结果相关的实体（保留原始导弹轨迹数据）
   */
  clearAnalysisResultEntities() {
    if (!this.viewer) return

    const analysisEntityPrefixes = [
      `${this.routeName}_intercept_`,
      `${this.routeName}_jamming_`,
      `${this.routeName}_awacs_`,
      `${this.routeName}_refuel_`,
      `${this.routeName}_rendezvous_`,
      `${this.routeName}_action_`,
      `${this.routeName}_fire_`,
      `${this.routeName}_task_`
    ]

    const entitiesToRemove = []

    // 遍历所有实体，找到分析结果相关的实体
    for (let i = 0; i < this.viewer.entities.values.length; i++) {
      const entity = this.viewer.entities.values[i]
      if (entity.id) {
        const shouldRemove = analysisEntityPrefixes.some(prefix => entity.id.startsWith(prefix))
        if (shouldRemove) {
          entitiesToRemove.push(entity.id)
        }
      }
    }

    // 删除找到的实体
    entitiesToRemove.forEach(entityId => {
      try {
        this.viewer.entities.removeById(entityId)
      } catch (error) {
        // 静默处理错误
      }
    })

  }

  /**
   * 清空导弹相关的实体（保留分析结果）
   */
  clearMissileEntities() {
    if (!this.viewer) return

    // 分析结果实体的前缀（需要保留的）
    const analysisResultPrefixes = [
      `${this.routeName}_task_`,
      `${this.routeName}_fire_`,
      `${this.routeName}_intercept_`,
      `${this.routeName}_jamming_`,
      `${this.routeName}_analysis_`
    ]

    const entitiesToRemove = []

    // 遍历所有实体，找到需要删除的导弹实体（排除分析结果）
    for (let i = 0; i < this.viewer.entities.values.length; i++) {
      const entity = this.viewer.entities.values[i]
      if (entity.id) {
        // 检查是否是当前路由的实体
        const isRouteEntity = entity.id.startsWith(`${this.routeName}_`)
        if (isRouteEntity) {
          // 检查是否是分析结果实体（需要保留）
          const isAnalysisResult = analysisResultPrefixes.some(prefix => entity.id.startsWith(prefix))
          if (!isAnalysisResult) {
            // 不是分析结果，可以删除
            entitiesToRemove.push(entity.id)
          }
        }
      }
    }

    // 删除找到的实体
    entitiesToRemove.forEach(entityId => {
      try {
        this.viewer.entities.removeById(entityId)
        if (this.callbacks.onEntityRemoved) {
          this.callbacks.onEntityRemoved(entityId)
        }
      } catch (error) {
        // 静默处理错误
      }
    })

    // 只清理导弹相关的实体ID，保留分析结果实体ID
    const remainingEntityIds = new Set()

    this.currentEntityIds.forEach(id => {
      // 检查是否是当前路由的实体
      const isRouteEntity = id.startsWith(`${this.routeName}_`)
      if (isRouteEntity) {
        // 检查是否是分析结果实体（需要保留）
        const isAnalysisResult = analysisResultPrefixes.some(prefix => id.startsWith(prefix))
        if (isAnalysisResult) {
          remainingEntityIds.add(id)
        }
      } else {
        // 不是当前路由的实体，保留
        remainingEntityIds.add(id)
      }
    })
    this.currentEntityIds = remainingEntityIds

  }

  /**
   * 提取轨迹数据
   */
  extractTrackData(missile) {
    // 兼容多种数据格式
    return missile.trackList ||
           missile.track_list ||
           missile.trajectory ||
           missile.path ||
           []
  }

  /**
   * 获取导弹图标配置
   */
  getMissileIconConfig(typeId) {
    if (this.typeIconMap.has(typeId)) {
      return this.typeIconMap.get(typeId)
    }

    const config = this.preGeneratedIcons[typeId] || this.preGeneratedIcons['DEFAULT']
    this.typeIconMap.set(typeId, config)
    return config
  }

  /**
   * 生成位置坐标数组
   */
  generatePositions(trackData) {
    const Cesium = window.Cesium
    const positions = []

    trackData.forEach(point => {
      try {
        const position = Cesium.Cartesian3.fromDegrees(
          parseFloat(point.lon || point.longitude),
          parseFloat(point.lat || point.latitude),
          parseFloat(point.alt || point.altitude || 0)
        )
        positions.push(position)
      } catch (error) {
        
      }
    })

    return positions
  }

  /**
   * 渲染导弹实体（轨迹点和线）
   */
  renderMissileEntities(missile, trackData, positions, iconConfig, typeId, newEntityIds) {
    const Cesium = window.Cesium

    // 渲染轨迹点
    trackData.forEach((point, pointIndex) => {
      const pointId = `${this.routeName}_${missile.id}_point_${pointIndex}`
      newEntityIds.add(pointId)

      // 检查实体是否已存在
      if (this.viewer.entities.getById(pointId)) {
        this.viewer.entities.removeById(pointId)
      }

      const position = positions[pointIndex]
      if (!position) return

      // 创建SVG图标
      const svgIcon = `
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
          <text x="20" y="25" font-size="24" text-anchor="middle" fill="${iconConfig.color}">${iconConfig.icon}</text>
        </svg>
      `;

      this.viewer.entities.add({
        id: pointId,
        position: position,
        billboard: {
          image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgIcon),
          scale: 0.8,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          color: Cesium.Color.WHITE,
          heightReference: Cesium.HeightReference.NONE
        },
        label: {
          text: `${iconConfig.name}\n时间: ${point.time}\n高度: ${point.alt || point.altitude || 0}m`,
          font: '10pt sans-serif',
          fillColor: Cesium.Color.fromCssColorString(iconConfig.color),
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -50), // 调整标签位置，避免遮挡图标
          showBackground: true,
          backgroundColor: Cesium.Color.BLACK.withAlpha(0.7)
        },
        show: missile.show !== false
      })

      if (this.callbacks.onEntityAdded) {
        this.callbacks.onEntityAdded(pointId, 'missile_point')
      }
    })

    // 渲染轨迹线
    if (positions.length > 1) {
      const polylineId = `${this.routeName}_${missile.id}_polyline`
      newEntityIds.add(polylineId)

      // 检查实体是否已存在
      if (this.viewer.entities.getById(polylineId)) {
        this.viewer.entities.removeById(polylineId)
      }

      this.viewer.entities.add({
        id: polylineId,
        polyline: {
          positions,
          width: 3,
          material: Cesium.Color.fromCssColorString(iconConfig.color).withAlpha(0.7)
        },
        show: missile.show !== false
      })

      if (this.callbacks.onEntityAdded) {
        this.callbacks.onEntityAdded(polylineId, 'missile_trajectory')
      }
    }
  }

  /**
   * 调整相机视角
   */
  adjustCameraView(allPositions) {
    if (!allPositions || allPositions.length === 0) return

    try {
      const Cesium = window.Cesium
      const rectangle = Cesium.Rectangle.fromCartesianArray(allPositions)
      const center = Cesium.Rectangle.center(rectangle)
      const offsetHeight = 2.5e6

      this.viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromRadians(
          center.longitude,
          center.latitude,
          rectangle.height + offsetHeight
        ),
        duration: 1.5
      })

      } catch (error) {
      }
  }

  /**
   * 销毁Cesium实例和清理资源
   */
  destroy() {
    try {
      // 清理所有实体
      this.clearAllEntities()

      // 清理缓存
      if (this.typeIconMap) {
        this.typeIconMap.clear()
        this.typeIconMap = null
      }

      // 销毁数据可视化工具
      if (this.dataVisualizationUtils) {
        this.dataVisualizationUtils = null
      }

      // 销毁Cesium viewer
      if (this.viewer) {
        this.viewer.destroy()
        this.viewer = null
      }

      // 清理回调
      this.callbacks = {}
    } catch (error) {
    }
  }

  /**
   * 获取当前实体统计信息
   */
  getEntityStats() {
    if (!this.viewer) {
      return { total: 0, route: 0 }
    }

    const total = this.viewer.entities.values.length
    const route = this.viewer.entities.values.filter(entity =>
      entity.id && entity.id.startsWith(`${this.routeName}_`)
    ).length

    return { total, route }
  }

  /**
   * 设置实体可见性
   */
  setEntityVisibility(entityId, visible) {
    if (!this.viewer) return

    const entity = this.viewer.entities.getById(entityId)
    if (entity) {
      entity.show = visible
    }
  }

  /**
   * 批量设置路由实体可见性
   */
  setRouteEntitiesVisibility(visible) {
    if (!this.viewer) return

    let count = 0
    this.viewer.entities.values.forEach(entity => {
      if (entity.id && entity.id.startsWith(`${this.routeName}_`)) {
        entity.show = visible
        count++
      }
    })

  }
}
