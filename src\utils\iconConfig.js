// 统一的实体文字图标配置
const iconConfig = {
  // 导弹实体
  missile: {
    HSFK: {
      color: '#FF0000', // 红色
      icon: '🚀',
      name: '红旗导弹'
    },
    DF41: {
      color: '#0080FF', // 蓝色
      icon: '🚀',
      name: '东风41'
    },
    DEFAULT: {
      color: '#FFD700', // 黄色
      icon: '🚀',
      name: '未知导弹'
    }
  },

  // 任务实体
  task: {
    AIRMOVE: {
      color: '#0080FF', // 蓝色
      icon: '✈️',
      name: '空中移动'
    },
    JAMLOC: {
      color: '#FFA500', // 橙色
      icon: '📡',
      name: '干扰定位'
    },
    MTGTLOC: {
      color: '#FF0000', // 红色
      icon: '🎯',
      name: '导弹目标定位'
    },
    GTGTLOC: {
      color: '#8B0000', // 深红色
      icon: '🏞️',
      name: '地面目标定位'
    },
    AMSNLOC: {
      color: '#00FF00', // 绿色
      icon: '🛡️',
      name: '防空导弹系统定位'
    },
    REFUEL: {
      color: '#FFD700', // 黄色
      icon: '⛽',
      name: '加油'
    },
    COMMRLY: {
      color: '#800080', // 紫色
      icon: '📶',
      name: '通信中继'
    }
  },

  // 火力策略
  fireStrategy: {
    DEFAULT: {
      color: '#FF0000', // 红色
      icon: '🔥',
      name: '火力策略'
    }
  },

  // 行动策略
  actionStrategy: {
    DEFAULT: {
      color: '#00BFFF', // 蓝色
      icon: '⚡',
      name: '行动策略'
    }
  },

  // 加油需求
  refuel: {
    high: {
      color: '#FF0000', // 红色
      icon: '⛽',
      name: '高紧急度加油'
    },
    medium: {
      color: '#FFA500', // 橙色
      icon: '⛽',
      name: '中紧急度加油'
    },
    low: {
      color: '#00FF00', // 绿色
      icon: '⛽',
      name: '低紧急度加油'
    }
  },

  // 干扰策略
  jammingStrategy: {
    DEFAULT: {
      color: '#FF6347', // 橙红色
      icon: '📡',
      name: '干扰策略'
    }
  },

  // 拦截实体
  intercept: {
    success: {
      color: '#00FF00', // 绿色
      icon: '✅',
      name: '拦截成功'
    },
    failure: {
      color: '#FF0000', // 红色
      icon: '❌',
      name: '拦截失败'
    }
  },

  // 预警机
  awacs: {
    active: {
      color: '#0080FF', // 蓝色
      icon: '🛩️',
      name: '活动预警机'
    },
    maintenance: {
      color: '#808080', // 灰色
      icon: '🛠️',
      name: '维护中预警机'
    },
    standby: {
      color: '#ADD8E6', // 浅蓝色
      icon: '✈️',
      name: '备用预警机'
    }
  },

  // 通信节点
  comm: {
    DEFAULT: {
      color: '#800080', // 紫色
      icon: '📶',
      name: '通信节点'
    }
  },

  // 雷达/频谱管理
  radar: {
    DEFAULT: {
      color: '#00FFFF', // 青色
      icon: '📡',
      name: '雷达/频谱管理'
    }
  }
};

export default iconConfig;