<!-- 通用侧卡片 -->
<template>
  <div class="side-card-container">
    <!-- 显示状态 -->
    <transition :name="position + '-slide-fade'">
      <div v-show="showCard" class="side-fixed" :class="position">
        <div class="tabs-row">
          <div
            v-for="(item, index) in contentList"
            :key="index"
            class="tab-item"
            :class="item.active ? 'active' : ''"
            @click="tabChange(index)"
          >
            <i
              v-if="
                item.icon !== '' &&
                  item.icon !== null &&
                  item.icon !== 'undefined'
              "
              :class="item.icon"
            />
            {{ item.name ? item.name : item.title }}
          </div>
        </div>
        <div class="main" :class="position">
          <el-scrollbar v-if="position==='left'" class="main-card">
            <div v-if="activeTab" class="tab-content">
              <h4>{{ activeTab.name || activeTab.title }}</h4>
              <div class="content-data">
                <div class="file-info">
                  <p><strong>文件名:</strong> {{ activeTab.fileName }}</p>
                  <p><strong>导入时间:</strong> {{ formatTime(activeTab.importTime) }}</p>
                </div>
                <div class="json-content">
                  <textarea v-model="jsonContent" class="json-textarea" @change="handleJsonChange"></textarea>
                </div>
              </div>
            </div>
            <div v-else class="no-content">
              <p>暂无数据</p>
              <p class="hint">请点击"导入数据"按钮导入JSON文件</p>
            </div>
          </el-scrollbar>
          <el-scrollbar v-else class="main-card">
            <div v-if="activeTab && activeTab.dataType === 'analysis_results'" class="tab-content">
              <h4>{{ activeTab.name || activeTab.title }}</h4>
              <div v-if="!resultData" class="no-result">
                <p>数据加载中...</p>
              </div>
              <div class="analysis-summary">
                <p class="summary-text">{{ activeTab.summary }}</p>
                <p class="analysis-time">分析时间: {{ formatTime(activeTab.resultData?.timestamp) }}</p>
              </div>

              <div v-if="activeTab.resultData" class="analysis-results">
                <!-- 任务分配信息 -->
                <div v-if="activeTab.resultData.taskAssignmentData && activeTab.resultData.taskAssignmentData['Task Orders'] && activeTab.resultData.taskAssignmentData['Task Orders'].length > 0" class="result-section">
                  <h5 class="section-title">📋 任务分配 ({{ activeTab.resultData.taskAssignmentData['Task Orders'].length }}个任务)</h5>
                  <div class="data-cards">
                    <div v-for="(item, index) in activeTab.resultData.taskAssignmentData['Task Orders']" :key="'task-' + index" class="data-card task-card">
                      <div class="card-header">
                        <span class="card-id">{{ item.MSGID }}</span>
                        <span class="card-status" :class="item.OPER">{{ item.OPER }}</span>
                      </div>
                      <div class="card-content">
                        <p><strong>操作类型:</strong> {{ item.OPER }}</p>
                        <p><strong>时间框架:</strong> {{ item.TIMEFRAM ? item.TIMEFRAM.join(' - ') : '未知' }}</p>
                        <p><strong>飞机数量:</strong> {{ item.MSNACFT ? item.MSNACFT['Aircraft Number'] : 0 }}</p>
                        <p><strong>飞机ID:</strong> {{ item.MSNACFT ? item.MSNACFT['Aircraft ID'].join(', ') : '无' }}</p>
                        <p v-if="item.AIRMOVE"><strong>空中移动:</strong> 有轨迹数据</p>
                        <p v-if="item.JAMLOC"><strong>干扰位置:</strong> {{ item.JAMLOC['Target ID'] }}</p>
                        <p v-if="item.MTGTLOC"><strong>导弹目标:</strong> {{ item.MTGTLOC['Target ID'] }}</p>
                        <p v-if="item.GTGTLOC"><strong>地面目标:</strong> {{ item.GTGTLOC['Target ID'] }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 火力策略信息 -->
                <div v-if="activeTab.resultData.fireStrategyData && activeTab.resultData.fireStrategyData.missile_strikes && activeTab.resultData.fireStrategyData.missile_strikes.length > 0" class="result-section">
                  <h5 class="section-title">🎯 火力策略 ({{ activeTab.resultData.fireStrategyData.missile_strikes.length }}个目标)</h5>
                  <div class="data-cards">
                    <div v-for="(item, index) in activeTab.resultData.fireStrategyData.missile_strikes" :key="'fire-' + index" class="data-card fire-card">
                      <div class="card-header">
                        <span class="card-id">{{ item.id }}</span>
                        <span class="card-type">{{ item.type }}</span>
                      </div>
                      <div class="card-content">
                        <p><strong>目标类型:</strong> {{ item.type }}</p>
                        <p><strong>打击顺序:</strong> {{ item.strike_order }}</p>
                        <p><strong>打击程度:</strong> {{ item.strike_degree }}</p>
                        <p><strong>目标位置:</strong> {{ item.position.longitude.toFixed(1) }}, {{ item.position.latitude.toFixed(1) }}</p>
                        <p><strong>所需弹药:</strong></p>
                        <ul v-if="item.required_ordnance">
                          <li v-for="(ordnance, idx) in item.required_ordnance" :key="idx">
                            {{ ordnance.ammunition_type }} × {{ ordnance.quantity }}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 行动策略信息 -->
                <div v-if="activeTab.resultData.actionStrategyData && activeTab.resultData.actionStrategyData.missile_actions && activeTab.resultData.actionStrategyData.missile_actions.length > 0" class="result-section">
                  <h5 class="section-title">🚁 行动策略 ({{ activeTab.resultData.actionStrategyData.missile_actions.length }}个行动)</h5>
                  <div class="data-cards">
                    <div v-for="(item, index) in activeTab.resultData.actionStrategyData.missile_actions" :key="'action-' + index" class="data-card action-card">
                      <div class="card-header">
                        <span class="card-id">{{ item.missile_id }}</span>
                        <span class="card-status">导弹行动</span>
                      </div>
                      <div class="card-content">
                        <p><strong>导弹ID:</strong> {{ item.missile_id }}</p>
                        <p v-if="item.key_time_nodes.discovery_time"><strong>发现时间:</strong> {{ item.key_time_nodes.discovery_time.relative_time }}</p>
                        <p v-if="item.key_time_nodes.stable_tracking_time"><strong>稳定跟踪:</strong> {{ item.key_time_nodes.stable_tracking_time.relative_time }}</p>
                        <p v-if="item.key_time_nodes.interceptable_period"><strong>可拦截期:</strong> {{ item.key_time_nodes.interceptable_period.relative_time }}</p>
                        <p v-if="item.key_time_nodes.jammable_period"><strong>可干扰期:</strong> {{ item.key_time_nodes.jammable_period.relative_time }}</p>
                        <p v-if="item.key_time_nodes.discovery_time"><strong>发现位置:</strong>
                          {{ item.key_time_nodes.discovery_time.start_position.longitude }},
                          {{ item.key_time_nodes.discovery_time.start_position.latitude }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 干扰决策信息 -->
                <div v-if="activeTab.resultData.jammingStrategyData && activeTab.resultData.jammingStrategyData['Jam Strategies'] && activeTab.resultData.jammingStrategyData['Jam Strategies'].length > 0" class="result-section">
                  <h5 class="section-title">📡 干扰决策 ({{ activeTab.resultData.jammingStrategyData['Jam Strategies'].length }}个策略)</h5>
                  <div class="data-cards">
                    <div v-for="(item, index) in activeTab.resultData.jammingStrategyData['Jam Strategies']" :key="'jamming-' + index" class="data-card jamming-card">
                      <div class="card-header">
                        <span class="card-id">{{ item.ID }}</span>
                        <span class="card-type">{{ item.Method }}</span>
                      </div>
                      <div class="card-content">
                        <p><strong>目标:</strong> {{ item['Target ID'] }}</p>
                        <p><strong>干扰方法:</strong> {{ item.Method }}</p>
                        <p><strong>优先级:</strong> {{ item.Priority }}</p>
                        <p><strong>时间:</strong> {{ item.Time || item['Release Time'] || '未知' }}</p>
                        <p v-if="item['Power(dBW)']"><strong>功率:</strong> {{ item['Power(dBW)'] }} dBW</p>
                        <p v-if="item['Central Frequency(GHz)']"><strong>中心频率:</strong> {{ item['Central Frequency(GHz)'] }} GHz</p>
                        <p v-if="item['Beam Width(MHz)']"><strong>波束宽度:</strong> {{ item['Beam Width(MHz)'] }} MHz</p>
                        <p v-if="item['Beam Width(GHz)']"><strong>波束宽度:</strong> {{ item['Beam Width(GHz)'] }} GHz</p>
                        <p v-if="item['Decoy Type']"><strong>诱饵类型:</strong> {{ item['Decoy Type'] }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 资源需求分析信息 -->
                <div v-if="resultData && resultData.resourceRequirementData" class="result-section">
                  <h5 class="section-title">⛽ 资源需求分析</h5>
                  
                  
                  <!-- 加油计划信息 -->
                  <div v-if="resultData.resourceRequirementData.refuelingPlan && resultData.resourceRequirementData.refuelingPlan.length > 0" class="sub-section">
                    <h6 class="sub-section-title">加油计划 ({{ resultData.resourceRequirementData.refuelingPlan.length }}个计划)</h6>
                    <div class="data-cards">
                      <div v-for="(item, index) in resultData.resourceRequirementData.refuelingPlan" :key="'refuel-' + index" class="data-card refuel-card">
                        <div class="card-header">
                          <span class="card-id">{{ item.aircraftId }}</span>
                          <span class="card-type">{{ item.missionType }}</span>
                        </div>
                        <div class="card-content">
                          <p><strong>飞机ID:</strong> {{ item.aircraftId }}</p>
                          <p><strong>类型:</strong> {{ item.type }}</p>
                          <p><strong>往返距离:</strong> {{ item.roundTripDistanceKm }} km</p>
                          <p><strong>最大航程:</strong> {{ item.maxRangeKm }} km</p>
                          <p><strong>是否需要加油:</strong> {{ item.refuelingRequired ? '是' : '否' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  
                  <!-- 弹药补给计划信息 -->
                  <div v-if="resultData.resourceRequirementData.ammoResupplyPlan" class="sub-section">
                    <h6 class="sub-section-title">弹药补给计划 ({{ Object.keys(resultData.resourceRequirementData.ammoResupplyPlan).length }}个基地)</h6>
                    <div class="data-cards">
                      <div v-for="(ammo, baseName) in resultData.resourceRequirementData.ammoResupplyPlan" :key="'ammo-' + baseName" class="data-card ammo-card">
                        <div class="card-header">
                          <span class="card-id">{{ baseName }}</span>
                          <span class="card-type">弹药基地</span>
                        </div>
                        <div class="card-content">
                          <p><strong>基地名称:</strong> {{ baseName }}</p>
                          <div v-for="(count, ammoType) in ammo" :key="'ammo-type-' + ammoType">
                            <p><strong>{{ ammoType }}:</strong> {{ count }}个</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  
                  <!-- 通信计划信息 -->
                  <div v-if="resultData.resourceRequirementData.communicationPlan && resultData.resourceRequirementData.communicationPlan.length > 0" class="sub-section">
                    <h6 class="sub-section-title">通信计划 ({{ resultData.resourceRequirementData.communicationPlan.length }}个节点)</h6>
                    <div class="data-cards">
                      <div v-for="(item, index) in resultData.resourceRequirementData.communicationPlan" :key="'comm-' + index" class="data-card comm-card">
                        <div class="card-header">
                          <span class="card-id">{{ item.client }}</span>
                          <span class="card-type">{{ item.type }}</span>
                        </div>
                        <div class="card-content">
                          <p><strong>客户端:</strong> {{ item.client }}</p>
                          <p><strong>类型:</strong> {{ item.type }}</p>
                          <p><strong>推荐中继:</strong> {{ item.recommendedRelay }}</p>
                          <p><strong>使用数据链:</strong> {{ item.usedDataLink }}</p>
                          <p><strong>状态:</strong> {{ item.status }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  
                  <!-- 频谱管理计划信息 -->
                  <div v-if="resultData.resourceRequirementData.spectrumManagementPlan" class="sub-section">
                    <h6 class="sub-section-title">频谱管理计划</h6>
                    
                    <!-- 雷达分配信息 -->
                    <div v-if="resultData.resourceRequirementData.spectrumManagementPlan.radarAssignments && resultData.resourceRequirementData.spectrumManagementPlan.radarAssignments.length > 0" class="sub-sub-section">
                      <h7 class="sub-sub-section-title">雷达分配 ({{ resultData.resourceRequirementData.spectrumManagementPlan.radarAssignments.length }}个)</h7>
                      <div class="data-cards">
                        <div v-for="(item, index) in resultData.resourceRequirementData.spectrumManagementPlan.radarAssignments" :key="'radar-' + index" class="data-card spectrum-card">
                          <div class="card-header">
                            <span class="card-id">{{ item.aircraftId }}</span>
                            <span class="card-type">{{ item.systemName }}</span>
                          </div>
                          <div class="card-content">
                            <p><strong>飞机ID:</strong> {{ item.aircraftId }}</p>
                            <p><strong>系统名称:</strong> {{ item.systemName }}</p>
                            <p><strong>分配频段:</strong> {{ item.assignedBand }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 干扰器分配信息 -->
                    <div v-if="resultData.resourceRequirementData.spectrumManagementPlan.jammerAssignments && resultData.resourceRequirementData.spectrumManagementPlan.jammerAssignments.length > 0" class="sub-sub-section">
                      <h7 class="sub-sub-section-title">干扰器分配 ({{ resultData.resourceRequirementData.spectrumManagementPlan.jammerAssignments.length }}个)</h7>
                      <div class="data-cards">
                        <div v-for="(item, index) in resultData.resourceRequirementData.spectrumManagementPlan.jammerAssignments" :key="'jammer-' + index" class="data-card spectrum-card">
                          <div class="card-header">
                            <span class="card-id">{{ item.aircraftId }}</span>
                            <span class="card-type">{{ item.systemName }}</span>
                          </div>
                          <div class="card-content">
                            <p><strong>飞机ID:</strong> {{ item.aircraftId }}</p>
                            <p><strong>系统名称:</strong> {{ item.systemName }}</p>
                            <p><strong>最终频段:</strong> {{ item.finalBand }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-result">
                <p>暂无计算结果</p>
                <p class="hint">请先导入数据并点击"计算分析"</p>
              </div>
            </div>
            <div v-else-if="activeTab" class="tab-content">
              <h4>{{ activeTab.name || activeTab.title }}</h4>
              <div class="content-data">
                <div v-if="resultData" class="result-content">
                  <pre>{{ JSON.stringify(resultData, null, 2) }}</pre>
                </div>
                <div v-else class="no-result">
                  <p>暂无计算结果</p>
                  <p class="hint">请先导入数据并点击"计算分析"</p>
                </div>
              </div>
            </div>
            <div v-else class="no-content">
              <p>暂无数据</p>
            </div>
          </el-scrollbar>
          <div class="view-btn" :class="position" @click="toggleShow">
            <i :class="position === 'left' ? 'el-icon-caret-left' : 'el-icon-caret-right'" />
          </div>
        </div>
      </div>
    </transition>
    <!-- 隐藏状态 -->
    <transition name="el-fade-in-linear">
      <div v-show="!showCard" class="side-hidden" :class="position">
        <div class="hidden-btn" :class="position" @click="toggleShow">
          <i :class="position === 'left' ? 'el-icon-caret-right' : 'el-icon-caret-left'" />
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'SideCardContainer',
  props: {
    position: {
      type: String,
      default: 'left'
      // required: true
    },
    contentList: {
      type: Array,
      default: () => []
      // required: true
    },
    // 路由名称
    routeName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showCard: true, // 显示/隐藏
      outputLoading: false, // 计算分析结果loading
      jsonContent: '' // 用于绑定文本域的JSON内容
    }
  },
  computed: {
    ...mapGetters(['getResultData']),
    // 获取当前激活的tab
    activeTab() {
      return this.contentList.find(item => item.active)
    },
    // 获取当前路由的结果数据
    resultData() {
      return this.routeName ? this.getResultData(this.routeName) : null
    }
  },
  watch: {
    // 监听activeTab变化，更新文本域内容
    activeTab: {
      handler(newVal) {
        if (newVal && newVal.value) {
          this.jsonContent = JSON.stringify(newVal.value, null, 2)
        } else {
          this.jsonContent = ''
        }
      },
      immediate: true
    }
  },
  methods: {
    // 切换tab
    tabChange(index) {
      // 如果是左侧卡片（jsonList），直接更新Vuex中的状态
      if (this.position === 'left') {
        // 调用Vuex action来更新激活状态
        this.setActiveJsonData({ routeName: this.routeName, activeIndex: index })
      } else {
        // 右侧卡片保持原有逻辑
        this.contentList.forEach((item, i) => {
          item.active = false
          if (i === index) {
            item.active = true
          }
        })
      }
    },
    // 侧边栏显示/隐藏
    toggleShow() {
      this.showCard = !this.showCard
      this.$emit('toggleShow', this.showCard)
    },
    // 格式化时间显示
    formatTime(timeString) {
      if (!timeString) return ''
      try {
        const date = new Date(timeString)
        return date.toLocaleString('zh-CN')
      } catch (error) {
        return timeString
      }
    },

    // 处理JSON内容变化
    handleJsonChange() {
      try {
        const parsedData = JSON.parse(this.jsonContent)
        if (this.activeTab) {
          // 更新当前tab的数据
          this.activeTab.value = parsedData
          // 如果需要同步到Vuex，可以在这里调用相应的action
          // 例如: this.updateJsonData({ routeName: this.routeName, data: parsedData })
        }
      } catch (error) {
        this.$message.error('JSON格式无效，请检查后再提交')
      }
    },

    // Vuex actions
    ...mapActions(['setActiveJsonData'])
  }
}
</script>
<style lang="scss" scoped>
// 侧边卡片样式
.side-fixed {
  height: calc(100% - 110px);
  width: 512px;
  position: fixed;
  top: 80px;

  &.left {
    left: 30px;

    .tabs-row {
      justify-content: flex-start;
    }

  }

  &.right {
    right: 30px;

    .tabs-row {
      justify-content: flex-end;
    }
  }

  .tabs-row {
    height: 28px;
    width: 100%;
    display: flex;
    align-items: stretch;
    gap: 4px;

    .tab-item {
      font-size: 14px;
      height: 100%;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: #042970;
      color: #A3C2FF;
      font-size: 14px;
      cursor: pointer;

      &.active {
        background: linear-gradient(180deg, #3877F2 2.17%, #042970 100%);
        color: #fff;
      }

      &:hover {
        color: #fff;
      }
    }
  }

  .main {
    height: calc(100% - 28px);
    width: 100%;
    display: flex;
    align-items: center;

    &.left {
      justify-content: row;
    }

    &.right {
      flex-direction: row-reverse;
    }

    .view-btn {
      width: 12px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background: #3877F2;
      cursor: pointer;
      margin-bottom: 78px;

      &.left {
        clip-path: polygon(0 0, 100% 8px, 100% 72px, 0 100%);
      }

      &.right {
        clip-path: polygon(0 8px, 100% 0, 100% 100%, 0 72px);
      }

      i {
        color: #A3C2FF;
        font-size: 18px;
      }

      &:hover {
        background: #5d91f9;

        i {
          color: #fff;
        }
      }
    }

    .main-card {
      height: 100%;
      width: calc(100% - 12px);
      padding: 16px;
      background: #021240e6;
      border: 1px solid #3877F2;
      box-shadow: 0 0 10px 0 #3877f2cc inset;

      .tab-content {
        color: #fff;

        h4 {
          margin: 0 0 16px 0;
          color: #A3C2FF;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #3877F2;
          padding-bottom: 8px;
        }

        .content-data {
          .file-info {
            background: rgba(56, 119, 242, 0.05);
            border: 1px solid rgba(56, 119, 242, 0.2);
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 16px;

            p {
              margin: 4px 0;
              color: #A3C2FF;
              font-size: 12px;

              strong {
                color: #fff;
              }
            }
          }

          .json-content {
      .json-textarea {
        background: rgba(56, 119, 242, 0.1);
        border: 1px solid rgba(56, 119, 242, 0.3);
        border-radius: 4px;
        padding: 12px;
        margin: 0;
        color: #A3C2FF;
        font-size: 12px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-x: auto;
        width: 100%;
        height: calc(100vh - 340px);

        resize: none;
        font-family: monospace;
      }
    }

          .hint {
            font-size: 12px;
            color: #A3C2FF;
            opacity: 0.7;
            margin-top: 8px;
          }

          .no-result {
            text-align: center;
            padding: 40px 20px;
            color: #909399;

            p {
              margin: 8px 0;
              font-size: 14px;
            }

            .hint {
              font-size: 12px;
              color: #A3C2FF;
              opacity: 0.7;
            }
          }

          .result-content {
            pre {
              background: rgba(76, 175, 80, 0.1);
              border: 1px solid rgba(76, 175, 80, 0.3);
              color: #81C784;
            }
          }
        }
      }

      .no-content {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;
        font-size: 14px;
      }

      ::v-deep .el-scrollbar__wrap{
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
        margin-right: unset !important;
        margin-bottom: unset !important;
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera*/
        }
      }
    }
  }
}

//隐藏卡片样式
.side-hidden {
  position: fixed;
  top: 0;
  height: 100dvh;
  height: 100vh;
  display: flex;
  align-items: center;

  &.left {
    left: 0;

    .hidden-btn {
      clip-path: polygon(0 0, 100% 8px, 100% 72px, 0 100%);
    }
  }

  &.right {
    right: 0;

    .hidden-btn {
      clip-path: polygon(0 8px, 100% 0, 100% 100%, 0 72px);
    }
  }

  .hidden-btn {
    width: 12px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: #3877F2;
    cursor: pointer;

    i {
      color: #A3C2FF;
      font-size: 18px;
    }

    &:hover {
      background: #5d91f9;

      i {
        color: #fff;
      }
    }
  }
}

// 分析结果样式
.analysis-summary {
  background: rgba(56, 119, 242, 0.08);
  border: 1px solid rgba(56, 119, 242, 0.25);
  padding: 16px;
  margin-bottom: 20px;
  border-radius: 6px;
  border-left: 4px solid #3877F2;
  box-shadow: 0 2px 8px rgba(56, 119, 242, 0.1);

  .summary-text {
    font-size: 14px;
    font-weight: 600;
    color: #A3C2FF;
    margin-bottom: 8px;
    line-height: 1.4;
  }

  .analysis-time {
    font-size: 12px;
    color: rgba(163, 194, 255, 0.8);
    margin: 0;
    opacity: 0.9;
  }
}

.result-section {
  margin-bottom: 28px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #A3C2FF;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(56, 119, 242, 0.3);
    display: flex;
    align-items: center;
    line-height: 1.3;

    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(135deg, #3877F2, #5d91f9);
      border-radius: 2px;
      margin-right: 8px;
      flex-shrink: 0;
    }
  }
}

.data-cards {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.data-card {
  background: rgba(56, 119, 242, 0.04);
  border: 1px solid rgba(56, 119, 242, 0.15);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(1px);

  &:hover {
    background: rgba(56, 119, 242, 0.08);
    border-color: rgba(56, 119, 242, 0.3);
    box-shadow: 0 4px 16px rgba(56, 119, 242, 0.15);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    transition-duration: 0.1s;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(56, 119, 242, 0.2);

    .card-id {
      font-weight: 600;
      color: #A3C2FF;
      font-size: 14px;
      font-family: 'Consolas', 'Monaco', monospace;
      letter-spacing: 0.5px;
    }

    .card-status, .card-type, .card-urgency {
      padding: 4px 10px;
      border-radius: 14px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border: 1px solid transparent;
      transition: all 0.2s ease;

      &.success {
        background: linear-gradient(135deg, rgba(103, 194, 58, 0.15), rgba(103, 194, 58, 0.08));
        color: #67c23a;
        border-color: rgba(103, 194, 58, 0.3);
      }

      &.failed {
        background: linear-gradient(135deg, rgba(245, 108, 108, 0.15), rgba(245, 108, 108, 0.08));
        color: #f56c6c;
        border-color: rgba(245, 108, 108, 0.3);
      }

      &.active {
        background: linear-gradient(135deg, rgba(56, 119, 242, 0.15), rgba(56, 119, 242, 0.08));
        color: #3877F2;
        border-color: rgba(56, 119, 242, 0.3);
      }

      &.maintenance {
        background: linear-gradient(135deg, rgba(144, 147, 153, 0.15), rgba(144, 147, 153, 0.08));
        color: #909399;
        border-color: rgba(144, 147, 153, 0.3);
      }

      &.high {
        background: linear-gradient(135deg, rgba(245, 108, 108, 0.15), rgba(245, 108, 108, 0.08));
        color: #f56c6c;
        border-color: rgba(245, 108, 108, 0.3);
      }

      &.medium {
        background: linear-gradient(135deg, rgba(230, 162, 60, 0.15), rgba(230, 162, 60, 0.08));
        color: #e6a23c;
        border-color: rgba(230, 162, 60, 0.3);
      }

      &.low {
        background: linear-gradient(135deg, rgba(103, 194, 58, 0.15), rgba(103, 194, 58, 0.08));
        color: #67c23a;
        border-color: rgba(103, 194, 58, 0.3);
      }

      &:hover {
        transform: scale(1.05);
      }
    }

    .card-type {
      background: linear-gradient(135deg, rgba(230, 162, 60, 0.15), rgba(230, 162, 60, 0.08));
      color: #e6a23c;
      border-color: rgba(230, 162, 60, 0.3);
    }
  }

  .card-content {
    p {
      margin: 8px 0;
      font-size: 13px;
      color: rgba(163, 194, 255, 0.9);
      line-height: 1.5;
      transition: color 0.2s ease;

      strong {
        color: #A3C2FF;
        font-weight: 600;
        min-width: 85px;
        display: inline-block;
        margin-right: 4px;
      }

      &:hover {
        color: #A3C2FF;
      }
    }

    ul {
      margin: 8px 0 8px 16px;
      padding: 0;

      li {
        margin: 4px 0;
        font-size: 12px;
        color: rgba(163, 194, 255, 0.8);
        line-height: 1.4;
        list-style-type: disc;

        &::marker {
          color: rgba(56, 119, 242, 0.6);
        }
      }
    }
  }
}

// 不同类型卡片的左边框颜色和特殊效果
.task-card {
  border-left: 4px solid #67c23a;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #67c23a, rgba(103, 194, 58, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.fire-card {
  border-left: 4px solid #f56c6c;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #f56c6c, rgba(245, 108, 108, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.action-card {
  border-left: 4px solid #3877F2;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3877F2, rgba(56, 119, 242, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.jamming-card {
  border-left: 4px solid #e6a23c;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #e6a23c, rgba(230, 162, 60, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

// 保留原有样式以防兼容性问题
.intercept-card {
  border-left: 4px solid #67c23a;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #67c23a, rgba(103, 194, 58, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.awacs-card {
  border-left: 4px solid #3877F2;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3877F2, rgba(56, 119, 242, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.refuel-card {
  border-left: 4px solid #4CAF50;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #4CAF50, rgba(76, 175, 80, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.ammo-card {
  border-left: 4px solid #FFC107;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #FFC107, rgba(255, 193, 7, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.comm-card {
  border-left: 4px solid #9C27B0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #9C27B0, rgba(156, 39, 176, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

.spectrum-card {
  border-left: 4px solid #00BCD4;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #00BCD4, rgba(0, 188, 212, 0.6));
    border-radius: 0 4px 4px 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .data-cards {
    gap: 12px;
  }

  .data-card {
    padding: 14px;

    .card-header {
      margin-bottom: 12px;

      .card-id {
        font-size: 13px;
      }
    }

    .card-content {
      p {
        font-size: 12px;
        margin: 6px 0;

        strong {
          min-width: 75px;
        }
      }
    }
  }

  .result-section {
    margin-bottom: 22px;

    .section-title {
      font-size: 15px;
      margin-bottom: 14px;
    }
  }
}

@media (max-width: 768px) {
  .analysis-summary {
    padding: 14px;
    margin-bottom: 16px;

    .summary-text {
      font-size: 13px;
    }

    .analysis-time {
      font-size: 11px;
    }
  }

  .data-card {
    padding: 12px;
    border-radius: 6px;

    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .card-status, .card-type, .card-urgency {
        font-size: 10px;
        padding: 3px 8px;
      }
    }
  }
}

// 滚动条优化
.main-card {
  ::v-deep .el-scrollbar__wrap {
    scrollbar-width: thin;
    scrollbar-color: rgba(56, 119, 242, 0.3) transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(56, 119, 242, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(56, 119, 242, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(56, 119, 242, 0.5);
      }
    }
  }
}

// 左侧卡片动效
.left-slide-fade-enter-active,
.left-slide-fade-leave-active {
  transition: all .2s ease;
}

.left-slide-fade-enter,
.left-slide-fade-leave-to

/* .slide-fade-leave-active in <2.1.8 */
  {
  transform: translateX(-500px);
}

.left-slide-fade-enter-to,
.left-slide-fade-leave

/* .slide-fade-leave-active in <2.1.8 */
  {
  transform: translateX(0);
}

//右侧卡片动效
.right-slide-fade-enter-active,
.right-slide-fade-leave-active {
  transition: all .2s ease;
}

.right-slide-fade-enter,
.right-slide-fade-leave-to

/* .slide-fade-leave-active in <2.1.8 */
  {
  transform: translateX(500px);
}

.right-slide-fade-enter-to,
.right-slide-fade-leave

/* .slide-fade-leave-active in <2.1.8 */
  {
  transform: translateX(0);
}
</style>
