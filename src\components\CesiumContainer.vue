<!--
 * @Description: cesium地图容器组件
 * @Author: tzs
 * @Date: 2025-07-04 09:07:49
 * @LastEditors: tzs
 * @LastEditTime: 2025-07-18 11:28:29
-->
<template>
  <div :id="cesiumContainerId" class="cesium-container" />
</template>

<script>
import { mapGetters } from 'vuex'
import { CesiumManager } from '@/utils/CesiumManager'

export default {
  name: 'CesiumContainer',
  props: {
    // 路由名称，用于区分不同的Cesium实例
    routeName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      cesiumManager: null, // Cesium管理器实例
      isInitialized: false // 初始化状态
    }
  },
  computed: {
    ...mapGetters(['getJsonList', 'getResultData']),
    // 动态生成Cesium容器ID，避免多路由冲突
    cesiumContainerId() {
      return `cesium-container-${this.routeName}`
    },
    // 获取当前路由的JSON数据列表
    jsonList() {
      return this.getJsonList(this.routeName)
    },
    // 获取当前路由的结果数据
    resultData() {
      return this.getResultData(this.routeName)
    }
  },
  watch: {
    // 监听jsonList变化和active状态变化
    jsonList: {
      handler(newJsonList, oldJsonList) {
        // 防抖处理，避免频繁更新
        this.debouncedRenderActiveData()
      },
      deep: true, // 需要深度监听以捕获active状态变化
      immediate: false
    },

    // 监听结果数据变化
    resultData: {
      handler(newResultData, oldResultData) {
        if (!this.cesiumManager || !this.isInitialized) {
          return
        }

        if (newResultData) {
          // 延迟一帧确保viewer已准备好
          this.$nextTick(() => {
            setTimeout(() => {
              this.cesiumManager.renderAnalysisResults(newResultData)
            }, 50)
          })
        } else {
          this.cesiumManager.clearAnalysisResultEntities()
        }
      },
      deep: true,
      immediate: false // 改为false，避免初始化时的冲突
    },

    // 监听路由变化，清理数据并重新渲染
    '$route'(newRoute, oldRoute) {
      if (newRoute.name !== oldRoute.name) {
        if (this.cesiumManager) {
          // 路由切换时强制清理所有实体，确保干净的状态
          this.cesiumManager.clearAllEntities()

          // 延迟一帧后检查新路由是否有数据需要渲染
          this.$nextTick(() => {
            setTimeout(() => {
              // 首先渲染导入数据（如果有的话）
              const currentJsonList = this.jsonList
              if (currentJsonList && currentJsonList.length > 0) {
                this.renderActiveData()
              }

              // 然后渲染分析结果（如果有的话）
              const currentResultData = this.resultData
              if (currentResultData) {
                this.cesiumManager.renderAnalysisResults(currentResultData)
              }
            }, 200) // 延迟200ms确保数据完全加载
          })
        }
      }
    }
  },
  async mounted() {
    console.log('🔧 CesiumContainer mounted for route:', this.routeName)

    try {
      // 初始化防抖函数
      this.initDebounce()

      // 初始化CesiumManager
      await this.initCesiumManager()

      // 延迟检查是否有数据需要渲染（用于路由恢复场景）
      this.$nextTick(() => {
        setTimeout(() => {
          // 首先渲染导入数据（如果有的话）
          const currentJsonList = this.jsonList
          if (currentJsonList && currentJsonList.length > 0) {
            this.renderActiveData()
          }

          // 然后渲染分析结果（如果有的话）
          const currentResultData = this.resultData
          if (currentResultData) {
            this.cesiumManager.renderAnalysisResults(currentResultData)
          }
        }, 500) // 延迟500ms确保Cesium完全初始化
      })
    } catch (error) {
      // 错误处理保持，但移除console
    }
  },

  beforeDestroy() {
    // 清理防抖函数
    if (this.debouncedRenderActiveData) {
      this.debouncedRenderActiveData.cancel()
      this.debouncedRenderActiveData = null
    }

    // 销毁CesiumManager
    if (this.cesiumManager) {
      this.cesiumManager.destroy()
      this.cesiumManager = null
    }

    this.isInitialized = false
  },
  methods: {
    /**
     * 初始化CesiumManager
     */
    async initCesiumManager() {
      try {
        // 创建CesiumManager实例
        this.cesiumManager = new CesiumManager(this.cesiumContainerId, this.routeName, {
          imageryUrl: 'http://localhost:38000/google_0-9/{z}/{x}/{y}.jpg',
          initialCamera: {
            longitude: 115,
            latitude: 40,
            height: 6000000
          }
        })

        // 设置事件回调
        this.cesiumManager.setCallback('onInitialized', (viewer) => {
          this.isInitialized = true
        })

        this.cesiumManager.setCallback('onError', (error) => {
          this.isInitialized = false
        })

        // 初始化Cesium
        await this.cesiumManager.initialize()
      } catch (error) {
        this.isInitialized = false
        throw error
      }
    },

    // 初始化防抖函数
    initDebounce() {
      // 简单的防抖实现
      let timeoutId = null
      this.debouncedRenderActiveData = () => {
        if (timeoutId) clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          this.renderActiveData()
        }, 100) // 100ms防抖
      }
      // 添加取消方法
      this.debouncedRenderActiveData.cancel = () => {
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
      }
    },

    // 渲染当前激活的数据
    renderActiveData() {
      if (!this.cesiumManager || !this.isInitialized) {
        return
      }

      // 查找激活的数据项
      const activeItem = this.jsonList.find(item => item.active)

      if (!activeItem) {
        return
      }

      // 提取导弹数据
      const missileData = this.extractMissileData(activeItem)

      if (!missileData || !Array.isArray(missileData)) {
        return
      }

      // 使用CesiumManager渲染导弹数据
      this.cesiumManager.renderMissileData(missileData)
    },

    // 提取导弹数据的辅助方法
    extractMissileData(activeItem) {
      if (!activeItem?.value) return null

      // 定义可能的字段名
      const possibleFields = ['missle_list', 'missleList', 'missileList', 'missiles']

      // 首先在value中查找
      for (const field of possibleFields) {
        if (activeItem.value[field]) {
          return activeItem.value[field]
        }
      }

      // 如果没找到，在嵌套的value中查找
      if (activeItem.value.value) {
        for (const field of possibleFields) {
          if (activeItem.value.value[field]) {
            return activeItem.value.value[field]
          }
        }
      }

      return null
    }
  }
}
</script>

<style>
.cesium-container {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 0;
}
.cesium-viewer-bottom{
  display: none;
}
</style>
