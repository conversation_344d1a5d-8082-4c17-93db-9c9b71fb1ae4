/**
   * 数据可视化工具类
   * 用于处理不同类型数据的地图标注和样式配置
   */

// 导入统一的图标配置
import iconConfig from './iconConfig';

export class DataVisualizationUtils {
  constructor(viewer) {
    this.viewer = viewer
    this.Cesium = window.Cesium
  }

  /**
   * 获取紧急程度文本
   * @param {string} urgencyLevel 紧急程度级别
   * @returns {string} 紧急程度文本描述
   */
  getUrgencyText(urgencyLevel) {
    const urgencyMap = {
      'high': '高',
      'medium': '中',
      'low': '低'
    }
    return urgencyMap[urgencyLevel] || '未知'
  }

  /**
   * 渲染任务分配信息
   * @param {Array} taskData 任务分配数据数组
   * @param {string} routeName 路由名称
   */
  renderTaskAssignmentData(taskData, routeName) {
    if (!taskData || !Array.isArray(taskData)) {
      return
    }

    if (!this.viewer) {
      return
    }

    taskData.forEach((task, index) => {
      // 从实际数据结构中提取位置信息
      let position = null
      const taskInfo = {
        id: task.MSGID || `TASK_${index}`,
        operation: task.OPER || '未知操作',
        timeFrame: task.TIMEFRAM || [],
        aircraftNumber: task.MSNACFT?.['Aircraft Number'] || 0
      }

      // 根据操作类型提取位置信息
      if (task.AIRMOVE && task.AIRMOVE.Track && task.AIRMOVE.Track.length > 0) {
        const track = task.AIRMOVE.Track[0] // 使用第一个轨迹点
        position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(track.longitude),
          parseFloat(track.latitude),
          parseFloat(track.altitude) * 1000 // 转换为米
        )
      } else if (task.JAMLOC && task.JAMLOC['Target ID']) {
        // 对于干扰任务，使用默认位置（可以根据目标ID查找具体位置）
        position = this.Cesium.Cartesian3.fromDegrees(
          116.0 + Math.random() * 2, // 随机位置，实际应该根据目标ID查找
          39.0 + Math.random() * 2,
          10000
        )
      } else if (task.MTGTLOC && task.MTGTLOC['Target ID']) {
        // 对于导弹目标任务
        position = this.Cesium.Cartesian3.fromDegrees(
          118.0 + Math.random() * 2,
          32.0 + Math.random() * 2,
          15000
        )
      } else if (task.GTGTLOC && task.GTGTLOC['Target ID']) {
        // 对于地面目标任务
        position = this.Cesium.Cartesian3.fromDegrees(
          110.0 + Math.random() * 2,
          20.0 + Math.random() * 2,
          5000
        )
      } else if (task.AMSNLOC && task.AMSNLOC.mission_position) {
        // 对于任务位置
        const pos = task.AMSNLOC.mission_position
        position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(pos.longitude),
          parseFloat(pos.latitude),
          parseFloat(pos.altitude) * 1000
        )
      } else if (task.REFUEL && task.REFUEL.Location) {
        // 对于加油任务
        const pos = task.REFUEL.Location
        position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(pos.longitude),
          parseFloat(pos.latitude),
          parseFloat(pos.altitude) * 1000
        )
      } else if (task.COMMRLY && task.COMMRLY.Location) {
        // 对于通信中继任务
        const pos = task.COMMRLY.Location
        position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(pos.longitude),
          parseFloat(pos.latitude),
          parseFloat(pos.altitude) * 1000
        )
      }

      if (!position) {
        return
      }

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('task', taskInfo.operation);

      // 创建任务实体
      const entityId = `${routeName}_task_${taskInfo.id}`

      try {
        const entity = this.viewer.entities.add({
          id: entityId,
          position: position,
          billboard: {
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createTaskIcon(taskInfo.operation)),
            scale: 0.8,
            verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
            color: color,
            heightReference: this.Cesium.HeightReference.NONE
          },
          label: {
            text: `${taskInfo.operation}\n${taskInfo.id}\n飞机数:${taskInfo.aircraftNumber}`,
            font: '12pt sans-serif',
            fillColor: color,
            outlineColor: this.Cesium.Color.BLACK,
            outlineWidth: 2,
            style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new this.Cesium.Cartesian2(0, -50),
            showBackground: true,
            backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
          },
          description: this.createTaskDescription(task)
        })
      } catch (error) {
        // 错误处理逻辑已移除日志
      }
    })
  }

  /**
   * 渲染火力策略信息
   * @param {Array} fireData 火力策略数据数组
   * @param {string} routeName 路由名称
   */
  renderFireStrategyData(fireData, routeName) {
    if (!fireData || !Array.isArray(fireData)) {
      return
    }

    fireData.forEach((fire, index) => {
      // 从实际数据结构中提取位置信息
      const position = this.Cesium.Cartesian3.fromDegrees(
        parseFloat(fire.position.longitude),
        parseFloat(fire.position.latitude),
        1000 // 地面目标，设置较低高度
      )

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('fireStrategy');

      // 创建火力目标实体
      const entityId = `${routeName}_fire_${fire.id}`

      try {
        const entity = this.viewer.entities.add({
          id: entityId,
          position: position,
          billboard: {
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createFireIcon()),
            scale: 0.8,
            verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
            color: color,
            heightReference: this.Cesium.HeightReference.NONE
          },
          label: {
            text: `${fire.type}\n${fire.strike_degree}\n顺序:${fire.strike_order}`,
            font: '12pt sans-serif',
            fillColor: color,
            outlineColor: this.Cesium.Color.BLACK,
            outlineWidth: 2,
            style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new this.Cesium.Cartesian2(0, -50),
            showBackground: true,
            backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
          },
          description: this.createFireDescription(fire)
        })
      } catch (error) {
        // 错误处理逻辑已移除日志
      }
    })
  }

  /**
   * 渲染行动策略信息
   * @param {Array} actionData 行动策略数据数组
   * @param {string} routeName 路由名称
   */
  renderActionStrategyData(actionData, routeName) {
    if (!actionData || !Array.isArray(actionData)) {
      return
    }

    actionData.forEach((action, index) => {
      // 从实际数据结构中提取位置信息
      let position = null

      // 使用发现时间的位置作为主要位置
      if (action.key_time_nodes && action.key_time_nodes.discovery_time && action.key_time_nodes.discovery_time.start_position) {
        const pos = action.key_time_nodes.discovery_time.start_position
        position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(pos.longitude),
          parseFloat(pos.latitude),
          20000 // 设置较高高度以区分
        )
      }

      if (!position) {
        return
      }

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('actionStrategy');

      // 创建行动实体
      const entityId = `${routeName}_action_${action.missile_id}`

      try {
        const entity = this.viewer.entities.add({
          id: entityId,
          position: position,
          billboard: {
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createActionIcon()),
            scale: 0.8,
            verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
            color: color,
            heightReference: this.Cesium.HeightReference.NONE
          },
          label: {
            text: `导弹行动\n${action.missile_id}\n发现时间:${action.key_time_nodes?.discovery_time?.relative_time || '未知'}`,
            font: '12pt sans-serif',
            fillColor: color,
            outlineColor: this.Cesium.Color.BLACK,
            outlineWidth: 2,
            style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new this.Cesium.Cartesian2(0, -50),
            showBackground: true,
            backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
          },
          description: this.createActionDescription(action)
        })
      } catch (error) {
        // 错误处理逻辑已移除日志
      }
    })
  }

  /**
   * 渲染资源需求分析数据
   * @param {Object} resourceData 资源需求分析数据对象
   * @param {string} routeName 路由名称
   */
  renderResourceRequirementData(resourceData, routeName) {
    // 渲染加油计划数据
    if (resourceData.refuelingPlan && Array.isArray(resourceData.refuelingPlan)) {
      this.renderRefuelingPlanData(resourceData.refuelingPlan, routeName)
    }

    // 渲染弹药补给计划数据
    if (resourceData.ammoResupplyPlan && Array.isArray(resourceData.ammoResupplyPlan)) {
      this.renderAmmoResupplyPlanData(resourceData.ammoResupplyPlan, routeName)
    }

    // 渲染通信计划数据
    if (resourceData.communicationPlan && Array.isArray(resourceData.communicationPlan)) {
      this.renderCommunicationPlanData(resourceData.communicationPlan, routeName)
    }

    // 渲染频谱管理计划数据
    if (resourceData.spectrumManagementPlan && Array.isArray(resourceData.spectrumManagementPlan)) {
      this.renderSpectrumManagementPlanData(resourceData.spectrumManagementPlan, routeName)
    }
  }

  /**
   * 渲染加油计划数据
   * @param {Array} refuelingData 加油计划数据数组
   * @param {string} routeName 路由名称
   */
  renderRefuelingPlanData(refuelingData, routeName) {
    refuelingData.forEach((refuel) => {
      // 提取加油点位置信息
      if (refuel.suggestedRefuelingPoints && Array.isArray(refuel.suggestedRefuelingPoints)) {
        refuel.suggestedRefuelingPoints.forEach((point, index) => {
          const position = this.Cesium.Cartesian3.fromDegrees(
            parseFloat(point.longitude),
            parseFloat(point.latitude),
            5000 // 设置高度为5000米
          )

          // 使用统一图标配置中的颜色
          const color = this.getIconColor('refuel', 'low');

          // 创建加油点实体
          const entityId = `${routeName}_refuel_${refuel.aircraftId}_${index}`

          try {
            const entity = this.viewer.entities.add({
              id: entityId,
              position: position,
              billboard: {
                image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createRefuelIcon()),
                scale: 0.8,
                verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
                color: color,
                heightReference: this.Cesium.HeightReference.NONE
              },
              label: {
                text: `加油点 ${index + 1}
飞机: ${refuel.aircraftId}
是否需要加油: ${refuel.refuelingRequired}
`,
                font: '12pt sans-serif',
                fillColor: color,
                outlineColor: this.Cesium.Color.BLACK,
                outlineWidth: 2,
                style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new this.Cesium.Cartesian2(0, -60),
                showBackground: true,
                backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
              },
              description: this.createRefuelDescription(refuel, point)
            })
          } catch (error) {
            // 错误处理逻辑已移除日志
          }
        })
      }
    })
  }

  /**
   * 渲染弹药补给计划数据
   * @param {Array} ammoData 弹药补给计划数据数组
   * @param {string} routeName 路由名称
   */
  renderAmmoResupplyPlanData(ammoData, routeName) {
    ammoData.forEach((ammo) => {
      // 基地位置
      if (ammo.baseLocation) {
        const position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(ammo.baseLocation.longitude),
          parseFloat(ammo.baseLocation.latitude),
          1000 // 设置较低高度
        )

        // 使用统一图标配置中的颜色
        const color = this.getIconColor('ammo');

        // 创建弹药基地实体
        const entityId = `${routeName}_ammo_${ammo.baseId}`

        try {
          const entity = this.viewer.entities.add({
            id: entityId,
            position: position,
            billboard: {
              image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createAmmoIcon()),
              scale: 0.8,
              verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
              color: color,
              heightReference: this.Cesium.HeightReference.NONE
            },
            label: {
              text: `弹药基地 ${ammo.baseId}
地空导弹: ${ammo.ammoInventory.surfaceToAirMissiles}
空空导弹: ${ammo.ammoInventory.airToAirMissiles}`,
              font: '12pt sans-serif',
              fillColor: color,
              outlineColor: this.Cesium.Color.BLACK,
              outlineWidth: 2,
              style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
              pixelOffset: new this.Cesium.Cartesian2(0, -50),
              showBackground: true,
              backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
            },
            description: this.createAmmoDescription(ammo)
          })
        } catch (error) {
          // 错误处理逻辑已移除日志
        }
      }
    })
  }

  /**
   * 渲染通信计划数据
   * @param {Array} commData 通信计划数据数组
   * @param {string} routeName 路由名称
   */
  renderCommunicationPlanData(commData, routeName) {
    commData.forEach((comm) => {
      // 通信节点位置
      if (comm.nodeLocation) {
        const position = this.Cesium.Cartesian3.fromDegrees(
          parseFloat(comm.nodeLocation.longitude),
          parseFloat(comm.nodeLocation.latitude),
          1500 // 设置高度
        )

        // 使用统一图标配置中的颜色
        // 使用统一图标配置中的颜色
        const color = this.getIconColor('comm');

        // 创建通信节点实体
        const entityId = `${routeName}_comm_${comm.nodeId}`

        try {
          const entity = this.viewer.entities.add({
            id: entityId,
            position: position,
            billboard: {
              image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createCommIcon()),
              scale: 0.8,
              verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
              color: color,
              heightReference: this.Cesium.HeightReference.NONE
            },
            label: {
              text: `通信节点 ${comm.nodeId}
类型: ${comm.nodeType}
频率: ${comm.frequencyRange} MHz`,
              font: '12pt sans-serif',
              fillColor: color,
              outlineColor: this.Cesium.Color.BLACK,
              outlineWidth: 2,
              style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
              pixelOffset: new this.Cesium.Cartesian2(0, -50),
              showBackground: true,
              backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
            },
            description: this.createCommDescription(comm)
          })
        } catch (error) {
          // 错误处理逻辑已移除日志
        }
      }
    })
  }

  /**
   * 渲染频谱管理计划数据
   * @param {Array} spectrumData 频谱管理计划数据数组
   * @param {string} routeName 路由名称
   */
  renderSpectrumManagementPlanData(spectrumData, routeName) {
    console.log('📶 渲染频谱管理计划数据', spectrumData)

    spectrumData.forEach((spectrum) => {
      // 雷达位置
      if (spectrum.radarLocations && Array.isArray(spectrum.radarLocations)) {
        spectrum.radarLocations.forEach((location, index) => {
          const position = this.Cesium.Cartesian3.fromDegrees(
            parseFloat(location.longitude),
            parseFloat(location.latitude),
            2000 // 设置高度
          )

          // 使用统一图标配置中的颜色
          const color = this.getIconColor('radar');

          // 创建雷达位置实体
          const entityId = `${routeName}_radar_${spectrum.radarId}_${index}`
          console.log(`📍 创建雷达位置实体: ${entityId}`)

          try {
            const entity = this.viewer.entities.add({
              id: entityId,
              position: position,
              billboard: {
                image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createRadarIcon()),
                scale: 0.8,
                verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
                color: color,
                heightReference: this.Cesium.HeightReference.NONE
              },
              label: {
                text: `雷达 ${spectrum.radarId}
频率: ${spectrum.frequency} MHz
功率: ${spectrum.power} kW`,
                font: '12pt sans-serif',
                fillColor: color,
                outlineColor: this.Cesium.Color.BLACK,
                outlineWidth: 2,
                style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new this.Cesium.Cartesian2(0, -50),
                showBackground: true,
                backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
              },
              description: this.createRadarDescription(spectrum, location)
            })
            console.log(`✅ 雷达位置实体创建成功: ${entityId}`, entity)
          } catch (error) {
            console.error(`❌ 雷达位置实体创建失败: ${entityId}`, error)
          }
        })
      }
    })
  }

  /**
   * 创建加油点图标
   */
  createRefuelIcon() {
    return `<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
      <circle cx="15" cy="15" r="12" fill="none" stroke="#00ff00" stroke-width="2"/>
      <path d="M10 15 L20 15 M15 10 L15 20" stroke="#00ff00" stroke-width="2"/>
      <path d="M15 5 L15 8 M5 15 L8 15 M15 22 L15 25 M22 15 L25 15" stroke="#00ff00" stroke-width="2"/>
    </svg>`
  }

  /**
   * 创建弹药图标
   */
  createAmmoIcon() {
      // 使用统一图标配置中的文字图标
      // 注意：iconConfig中没有直接的'ammo'类型，这里使用'missile'作为替代
      const icon = iconConfig.missile.DEFAULT.icon;
      // 创建包含文字图标的简单SVG
      return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
              </svg>`;
    }

  /**
   * 创建通信图标
   */
  createCommIcon() {
    // 使用统一图标配置中的文字图标
    const icon = iconConfig.comm.DEFAULT.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  /**
   * 创建雷达图标
   */
  createRadarIcon() {
    // 使用统一图标配置中的文字图标
    const icon = iconConfig.radar.DEFAULT.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  /**
   * 创建加油点描述
   */
  createRefuelDescription(refuel, point) {
    return `<div style="font-family: sans-serif; padding: 5px;">
      <h3>加油计划详情</h3>
      <p><strong>飞机ID:</strong> ${refuel.aircraftId}</p>
      <p><strong>任务类型:</strong> ${refuel.missionType}</p>
      <p><strong>航程:</strong> ${refuel.range} km</p>
      <p><strong>燃油需求:</strong> ${refuel.fuelRequirement} kg</p>
      <p><strong>加油点经纬度:</strong> ${point.latitude}, ${point.longitude}</p>
      <p><strong>加油时间:</strong> ${point.refuelingTime}</p>
    </div>`
  }

  /**
   * 创建弹药基地描述
   */
  createAmmoDescription(ammo) {
    return `<div style="font-family: sans-serif; padding: 5px;">
      <h3>弹药基地详情</h3>
      <p><strong>基地ID:</strong> ${ammo.baseId}</p>
      <p><strong>基地类型:</strong> ${ammo.baseType}</p>
      <p><strong>位置:</strong> ${ammo.baseLocation.latitude}, ${ammo.baseLocation.longitude}</p>
      <h4>弹药库存:</h4>
      <p><strong>地空导弹:</strong> ${ammo.ammoInventory.surfaceToAirMissiles}</p>
      <p><strong>空空导弹:</strong> ${ammo.ammoInventory.airToAirMissiles}</p>
      <p><strong>空地导弹:</strong> ${ammo.ammoInventory.airToGroundMissiles}</p>
      <p><strong>高炮弹药:</strong> ${ammo.ammoInventory.antiAircraftAmmo}</p>
    </div>`
  }

  /**
   * 创建通信节点描述
   */
  createCommDescription(comm) {
    return `<div style="font-family: sans-serif; padding: 5px;">
      <h3>通信节点详情</h3>
      <p><strong>节点ID:</strong> ${comm.nodeId}</p>
      <p><strong>节点类型:</strong> ${comm.nodeType}</p>
      <p><strong>位置:</strong> ${comm.nodeLocation.latitude}, ${comm.nodeLocation.longitude}</p>
      <p><strong>频率范围:</strong> ${comm.frequencyRange} MHz</p>
      <p><strong>功率:</strong> ${comm.power} W</p>
      <p><strong>通信范围:</strong> ${comm.communicationRange} km</p>
      <p><strong>加密类型:</strong> ${comm.encryptionType}</p>
    </div>`
  }

  /**
   * 创建雷达描述
   */
  createRadarDescription(radar, location) {
    return `<div style="font-family: sans-serif; padding: 5px;">
      <h3>雷达详情</h3>
      <p><strong>雷达ID:</strong> ${radar.radarId}</p>
      <p><strong>雷达类型:</strong> ${radar.radarType}</p>
      <p><strong>位置:</strong> ${location.latitude}, ${location.longitude}</p>
      <p><strong>频率:</strong> ${radar.frequency} MHz</p>
      <p><strong>功率:</strong> ${radar.power} kW</p>
      <p><strong>探测范围:</strong> ${radar.detectionRange} km</p>
      <p><strong>方位角范围:</strong> ${radar.azimuthRange}°</p>
      <p><strong>俯仰角范围:</strong> ${radar.elevationRange}°</p>
    </div>`
  }

  /**
   * 渲染干扰决策信息
   * @param {Array} jammingData 干扰决策数据数组
   * @param {string} routeName 路由名称
   */
  renderJammingStrategyData(jammingData, routeName) {
    console.log('📡 DataVisualizationUtils: 开始渲染干扰决策数据', jammingData)

    if (!jammingData || !Array.isArray(jammingData)) {
      console.warn('⚠️ 干扰决策数据无效或为空')
      return
    }

    console.log(`📊 准备渲染 ${jammingData.length} 个干扰策略`)

    jammingData.forEach((jamming, index) => {
      console.log(`📡 渲染干扰策略 ${index + 1}:`, jamming)

      let position
      let altitude = 12000 // 默认高度

      // 优先使用 Decoy Location 中的精确坐标（无源干扰）
      if (jamming['Decoy Location'] && Array.isArray(jamming['Decoy Location']) && jamming['Decoy Location'].length > 0) {
        const decoyPos = jamming['Decoy Location'][0] // 使用第一个诱饵位置
        const longitude = parseFloat(decoyPos.longitude)
        const latitude = parseFloat(decoyPos.latitude)
        altitude = parseFloat(decoyPos.altitude) * 1000 // 转换为米

        console.log(`📍 使用诱饵位置: ${longitude}°E, ${latitude}°N, ${altitude}m`)

        position = this.Cesium.Cartesian3.fromDegrees(longitude, latitude, altitude)
      } else {
        // 为其他干扰策略生成位置（基于目标ID和策略类型）
        const basePositions = {
          'HXFK01': { lon: 121.0, lat: 26.0 },
          'HXFK02': { lon: 119.0, lat: 24.0 },
          'HXFK03': { lon: 117.0, lat: 22.0 }
        }

        const basePos = basePositions[jamming['Target ID']] || { lon: 118.0, lat: 25.0 }

        // 根据干扰方法调整位置
        const methodOffset = {
          '压制': { lonOffset: 0.1, latOffset: 0.1 },
          '欺骗': { lonOffset: -0.1, latOffset: 0.1 },
          '无源': { lonOffset: 0, latOffset: -0.1 }
        }

        const offset = methodOffset[jamming.Method] || { lonOffset: 0, latOffset: 0 }

        console.log(`📍 生成相对位置: ${basePos.lon + offset.lonOffset}°E, ${basePos.lat + offset.latOffset}°N`)

        position = this.Cesium.Cartesian3.fromDegrees(
          basePos.lon + offset.lonOffset,
          basePos.lat + offset.latOffset,
          altitude
        )
      }

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('jammingStrategy');

      // 创建干扰策略实体
      const entityId = `${routeName}_jamming_${jamming.ID}`
      console.log(`📍 创建干扰策略实体: ${entityId}`)

      try {
        const entity = this.viewer.entities.add({
          id: entityId,
          position: position,
          billboard: {
            image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createJammingStrategyIcon()),
            scale: 0.8,
            verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
            color: color,
            heightReference: this.Cesium.HeightReference.NONE
          },
          label: {
            text: `${jamming.Method}干扰\n${jamming.ID}\n目标:${jamming['Target ID']}`,
            font: '12pt sans-serif',
            fillColor: color,
            outlineColor: this.Cesium.Color.BLACK,
            outlineWidth: 2,
            style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new this.Cesium.Cartesian2(0, -50),
            showBackground: true,
            backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
          },
          description: this.createJammingStrategyDescription(jamming)
        })
        console.log(`✅ 干扰策略实体创建成功: ${entityId}`, entity)
      } catch (error) {
        console.error(`❌ 干扰策略实体创建失败: ${entityId}`, error)
      }
    })
  }

  /**
   * 渲染导弹拦截信息（保留原有方法以防兼容性问题）
   * @param {Array} interceptData 拦截数据数组
   * @param {string} routeName 路由名称
   */
  renderInterceptData(interceptData, routeName) {
    console.log('🎯 DataVisualizationUtils: 开始渲染拦截数据', interceptData)

    if (!interceptData || !Array.isArray(interceptData)) {
      console.warn('⚠️ 拦截数据无效或为空')
      return
    }

    if (!this.viewer) {
      console.error('❌ Cesium viewer 未初始化')
      return
    }

    console.log(`📊 准备渲染 ${interceptData.length} 个拦截点`)

    interceptData.forEach((intercept, index) => {
      console.log(`🎯 渲染拦截点 ${index + 1}:`, intercept)
      const position = this.Cesium.Cartesian3.fromDegrees(
        intercept.interceptPosition.longitude,
        intercept.interceptPosition.latitude,
        intercept.interceptPosition.altitude
      )

      // 根据拦截结果选择颜色和图标
      const isSuccess = intercept.interceptResult === 'success'
      // 使用统一图标配置中的颜色
      const color = this.getIconColor('intercept', isSuccess ? 'success' : 'fail');
      // 使用简单的几何形状作为图标，避免中文字符编码问题
      const iconUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createInterceptIcon(isSuccess))

      // 创建拦截点实体
      const entityId = `${routeName}_intercept_${intercept.interceptId}`
      console.log(`📍 创建拦截实体: ${entityId}`)

      try {
        const entity = this.viewer.entities.add({
          id: entityId,
          position: position,
          billboard: {
            image: iconUrl,
            scale: 0.8,
            verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
            color: color,
            heightReference: this.Cesium.HeightReference.NONE // 使用绝对高度
          },
          label: {
            text: `拦截${isSuccess ? '成功' : '失败'}\n${intercept.interceptTime.split('T')[1].split('.')[0]}\n${intercept.interceptorType}`,
            font: '12pt sans-serif',
            fillColor: color,
            outlineColor: this.Cesium.Color.BLACK,
            outlineWidth: 2,
            style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new this.Cesium.Cartesian2(0, -50),
            showBackground: true,
            backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
          },
          description: this.createInterceptDescription(intercept)
        })
        console.log(`✅ 拦截实体创建成功: ${entityId}`, entity)
      } catch (error) {
        console.error(`❌ 拦截实体创建失败: ${entityId}`, error)
      }

      // 创建拦截范围圆圈
      const rangeEntityId = `${routeName}_intercept_range_${intercept.interceptId}`
      console.log(`🎯 创建拦截范围: ${rangeEntityId}`)

      try {
        const rangeEntity = this.viewer.entities.add({
          id: rangeEntityId,
          position: position,
          ellipse: {
            semiMajorAxis: intercept.interceptDistance * 1000, // 转换为米
            semiMinorAxis: intercept.interceptDistance * 1000,
            material: color.withAlpha(0.3),
            outline: true,
            outlineColor: color,
            height: intercept.interceptPosition.altitude
          }
        })
        console.log(`✅ 拦截范围创建成功: ${rangeEntityId}`, rangeEntity)
      } catch (error) {
        console.error(`❌ 拦截范围创建失败: ${rangeEntityId}`, error)
      }
    })
  }

  /**
   * 渲染预警机位置
   * @param {Array} awacsData 预警机数据数组
   * @param {string} routeName 路由名称
   */
  renderAwacsData(awacsData, routeName) {
    console.log('✈️ DataVisualizationUtils: 开始渲染预警机数据', awacsData)
    if (!awacsData || !Array.isArray(awacsData)) return

    awacsData.forEach((awacs) => {
      const position = this.Cesium.Cartesian3.fromDegrees(
        awacs.currentPosition.longitude,
        awacs.currentPosition.latitude,
        awacs.currentPosition.altitude
      )

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('awacs', awacs.operationalStatus);

      // 创建预警机实体
      const entityId = `${routeName}_awacs_${awacs.awacsId}`
      this.viewer.entities.add({
        id: entityId,
        position: position,
        billboard: {
          image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createAwacsIcon()),
          scale: 1.0,
          verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
          color: color,
          heightReference: this.Cesium.HeightReference.NONE // 使用绝对高度
        },
        label: {
          text: `${awacs.callSign}\n${awacs.aircraftType}\n燃油:${Math.round(awacs.fuelLevel * 100)}%`,
          font: '12pt sans-serif',
          fillColor: color,
          outlineColor: this.Cesium.Color.BLACK,
          outlineWidth: 2,
          style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new this.Cesium.Cartesian2(0, -60),
          showBackground: true,
          backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
        },
        description: this.createAwacsDescription(awacs)
      })

      // 创建雷达覆盖范围
      this.viewer.entities.add({
        id: `${routeName}_awacs_radar_${awacs.awacsId}`,
        position: position,
        ellipse: {
          semiMajorAxis: awacs.radarRange * 1000, // 转换为米
          semiMinorAxis: awacs.radarRange * 1000,
          material: color.withAlpha(0.15),
          outline: true,
          outlineColor: color,
          height: awacs.currentPosition.altitude
        }
      })

      // 渲染巡逻路线
      if (awacs.patrolRoute && awacs.patrolRoute.length > 1) {
        const routePositions = awacs.patrolRoute.map(point =>
          this.Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude, point.altitude)
        )

        this.viewer.entities.add({
          id: `${routeName}_awacs_route_${awacs.awacsId}`,
          polyline: {
            positions: routePositions,
            width: 3,
            material: color.withAlpha(0.8),
            clampToGround: false
          }
        })
      }
    })
  }

  /**
   * 渲染加油需求
   * @param {Array} refuelData 加油需求数据数组
   * @param {string} routeName 路由名称
   */
  renderRefuelData(refuelData, routeName) {
    console.log('⛽ DataVisualizationUtils: 开始渲染加油数据', refuelData)
    if (!refuelData || !Array.isArray(refuelData)) return

    refuelData.forEach((refuel) => {
      const position = this.Cesium.Cartesian3.fromDegrees(
        refuel.currentPosition.longitude,
        refuel.currentPosition.latitude,
        refuel.currentPosition.altitude
      )

      // 使用统一图标配置中的颜色
      const color = this.getIconColor('refuel', refuel.urgencyLevel);

      // 创建需要加油的飞机实体
      const entityId = `${routeName}_refuel_${refuel.actionId}`
      this.viewer.entities.add({
        id: entityId,
        position: position,
        billboard: {
          image: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(this.createRefuelIcon()),
          scale: 0.8,
          verticalOrigin: this.Cesium.VerticalOrigin.BOTTOM,
          color: color,
          heightReference: this.Cesium.HeightReference.NONE // 使用绝对高度
        },
        label: {
          text: `${refuel.callSign}\n燃油:${Math.round(refuel.fuelLevel * 100)}%\n紧急度:${this.getUrgencyText(refuel.urgencyLevel)}`,
          font: '11pt sans-serif',
          fillColor: color,
          outlineColor: this.Cesium.Color.BLACK,
          outlineWidth: 2,
          style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new this.Cesium.Cartesian2(0, -50),
          showBackground: true,
          backgroundColor: this.Cesium.Color.BLACK.withAlpha(0.7)
        },
        description: this.createRefuelDescription(refuel)
      })

      // 如果有会合点，创建连接线和会合点
      if (refuel.rendezvousPoint) {
        const rendezvousPosition = this.Cesium.Cartesian3.fromDegrees(
          refuel.rendezvousPoint.longitude,
          refuel.rendezvousPoint.latitude,
          refuel.rendezvousPoint.altitude
        )

        // 连接线
        this.viewer.entities.add({
          id: `${routeName}_refuel_line_${refuel.actionId}`,
          polyline: {
            positions: [position, rendezvousPosition],
            width: 2,
            material: color.withAlpha(0.8),
            clampToGround: false
          }
        })

        // 会合点
        this.viewer.entities.add({
          id: `${routeName}_rendezvous_${refuel.actionId}`,
          position: rendezvousPosition,
          point: {
            pixelSize: 10,
            color: color,
            outlineColor: this.Cesium.Color.WHITE,
            outlineWidth: 2,
            heightReference: this.Cesium.HeightReference.CLAMP_TO_GROUND
          },
          label: {
            text: '会合点',
            font: '10pt sans-serif',
            fillColor: color,
            pixelOffset: new this.Cesium.Cartesian2(0, -20)
          }
        })
      }
    })
  }

  // 辅助方法

  // 创建详细描述的方法
  createInterceptDescription(intercept) {
    return `
      <h3>导弹拦截信息</h3>
      <p><strong>拦截ID:</strong> ${intercept.interceptId}</p>
      <p><strong>目标导弹:</strong> ${intercept.targetMissileId}</p>
      <p><strong>拦截器:</strong> ${intercept.interceptorId}</p>
      <p><strong>拦截时间:</strong> ${intercept.interceptTime}</p>
      <p><strong>拦截结果:</strong> ${intercept.interceptResult === 'success' ? '成功' : '失败'}</p>
      <p><strong>拦截器类型:</strong> ${intercept.interceptorType}</p>
      <p><strong>目标类型:</strong> ${intercept.targetType}</p>
      <p><strong>拦截距离:</strong> ${intercept.interceptDistance} km</p>
      <p><strong>描述:</strong> ${intercept.description}</p>
    `
  }

  createAwacsDescription(awacs) {
    return `
      <h3>预警机信息</h3>
      <p><strong>预警机ID:</strong> ${awacs.awacsId}</p>
      <p><strong>呼号:</strong> ${awacs.callSign}</p>
      <p><strong>机型:</strong> ${awacs.aircraftType}</p>
      <p><strong>雷达范围:</strong> ${awacs.radarRange} km</p>
      <p><strong>运行状态:</strong> ${awacs.operationalStatus}</p>
      <p><strong>任务:</strong> ${awacs.mission}</p>
      <p><strong>机组人员:</strong> ${awacs.crew} 人</p>
      <p><strong>燃油水平:</strong> ${Math.round(awacs.fuelLevel * 100)}%</p>
      <p><strong>描述:</strong> ${awacs.description}</p>
    `
  }

  createRefuelDescription(refuel) {
    return `
      <h3>加油需求信息</h3>
      <p><strong>行动ID:</strong> ${refuel.actionId}</p>
      <p><strong>飞机ID:</strong> ${refuel.aircraftId}</p>
      <p><strong>机型:</strong> ${refuel.aircraftType}</p>
      <p><strong>呼号:</strong> ${refuel.callSign}</p>
      <p><strong>当前燃油:</strong> ${refuel.currentFuel} L (${Math.round(refuel.fuelLevel * 100)}%)</p>
      <p><strong>需要燃油:</strong> ${refuel.requiredFuel} L</p>
      <p><strong>紧急程度:</strong> ${this.getUrgencyText(refuel.urgencyLevel)}</p>
      <p><strong>预计飞行时间:</strong> ${refuel.estimatedFlightTime} 分钟</p>
      <p><strong>最近加油机:</strong> ${refuel.nearestTanker}</p>
      <p><strong>加油机距离:</strong> ${refuel.tankerDistance} km</p>
      <p><strong>预计加油时间:</strong> ${refuel.estimatedRefuelTime}</p>
      <p><strong>任务:</strong> ${refuel.mission}</p>
      <p><strong>描述:</strong> ${refuel.description}</p>
    `
  }

  // 创建SVG图标的方法
  createInterceptIcon(isSuccess) {
    const config = isSuccess ? iconConfig.intercept.success : iconConfig.intercept.failure;
    const icon = config.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  createJammingIcon(jammingType) {
    // 干扰类型的图标配置
    const config = iconConfig.jammingStrategy.DEFAULT;
    const icon = config.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  createAwacsIcon(operationalStatus = 'active') {
    const config = iconConfig.awacs[operationalStatus] || iconConfig.awacs.active;
    const icon = config.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  createRefuelIcon(urgencyLevel = 'medium') {
      // 使用统一图标配置中的文字图标
      const config = iconConfig.refuel[urgencyLevel] || iconConfig.refuel.medium;
      const icon = config.icon;
      // 创建包含文字图标的简单SVG
      return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
              </svg>`;
    }

  // 新增图标方法
  createTaskIcon(operation) {
      // 使用统一图标配置中的文字图标
      const config = iconConfig.task[operation] || iconConfig.task.DEFAULT;
      const icon = config.icon;
      // 创建包含文字图标的简单SVG
      return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
              </svg>`;
    }

  createFireIcon() {
      // 使用统一图标配置中的文字图标
      const icon = iconConfig.fireStrategy.DEFAULT.icon;
      // 创建包含文字图标的简单SVG
      return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
              </svg>`;
    }

  createActionIcon() {
      // 使用统一图标配置中的文字图标
      const icon = iconConfig.actionStrategy.DEFAULT.icon;
      // 创建包含文字图标的简单SVG
      return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
              </svg>`;
    }

  createJammingStrategyIcon() {
    const config = iconConfig.jammingStrategy.DEFAULT;
    const icon = config.icon;
    // 创建包含文字图标的简单SVG
    return `<svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
              <text x="16" y="24" font-size="24" text-anchor="middle" font-family="Arial, sans-serif">${icon}</text>
            </svg>`;
  }

  // 获取图标颜色的辅助方法
  getIconColor(entityType, subtype = 'DEFAULT') {
    // 将Cesium颜色字符串转换为Cesium.Color对象
    const colorStr = iconConfig[entityType] && iconConfig[entityType][subtype] ? 
      iconConfig[entityType][subtype].color : '#808080';
    return this.Cesium.Color.fromCssColorString(colorStr);
  }

  // 新增描述方法
  createTaskDescription(task) {
    return `
      <h3>任务分配信息</h3>
      <p><strong>消息ID:</strong> ${task.MSGID}</p>
      <p><strong>操作类型:</strong> ${task.OPER}</p>
      <p><strong>时间框架:</strong> ${task.TIMEFRAM ? task.TIMEFRAM.join(' - ') : '未知'}</p>
      <p><strong>飞机数量:</strong> ${task.MSNACFT ? task.MSNACFT['Aircraft Number'] : 0}</p>
      <p><strong>飞机ID:</strong> ${task.MSNACFT ? task.MSNACFT['Aircraft ID'].join(', ') : '无'}</p>
      ${task.AIRMOVE ? '<p><strong>空中移动:</strong> 有轨迹数据</p>' : ''}
      ${task.JAMLOC ? `<p><strong>干扰目标:</strong> ${task.JAMLOC['Target ID']}</p>` : ''}
      ${task.MTGTLOC ? `<p><strong>导弹目标:</strong> ${task.MTGTLOC['Target ID']}</p>` : ''}
      ${task.GTGTLOC ? `<p><strong>地面目标:</strong> ${task.GTGTLOC['Target ID']}</p>` : ''}
    `
  }

  createFireDescription(fire) {
    return `
      <h3>火力策略信息</h3>
      <p><strong>目标ID:</strong> ${fire.id}</p>
      <p><strong>目标类型:</strong> ${fire.type}</p>
      <p><strong>打击顺序:</strong> ${fire.strike_order}</p>
      <p><strong>打击程度:</strong> ${fire.strike_degree}</p>
      <p><strong>目标位置:</strong> ${fire.position.longitude}, ${fire.position.latitude}</p>
      <p><strong>所需弹药:</strong></p>
      <ul>
        ${fire.required_ordnance
    ? fire.required_ordnance.map(ord =>
      `<li>${ord.ammunition_type} × ${ord.quantity}</li>`
    ).join('')
    : '<li>无弹药信息</li>'}
      </ul>
    `
  }

  createActionDescription(action) {
    return `
      <h3>行动策略信息</h3>
      <p><strong>导弹ID:</strong> ${action.missile_id}</p>
      <p><strong>发现时间:</strong> ${action.key_time_nodes?.discovery_time?.relative_time || '未知'}</p>
      <p><strong>稳定跟踪时间:</strong> ${action.key_time_nodes?.stable_tracking_time?.relative_time || '未知'}</p>
      <p><strong>可拦截期:</strong> ${action.key_time_nodes?.interceptable_period?.relative_time || '未知'}</p>
      <p><strong>可干扰期:</strong> ${action.key_time_nodes?.jammable_period?.relative_time || '未知'}</p>
      <p><strong>发现位置:</strong> ${action.key_time_nodes?.discovery_time?.start_position
    ? `${action.key_time_nodes.discovery_time.start_position.longitude}, ${action.key_time_nodes.discovery_time.start_position.latitude}`
    : '未知'}</p>
    `
  }

  createJammingStrategyDescription(jamming) {
    return `
      <h3>干扰决策信息</h3>
      <p><strong>策略ID:</strong> ${jamming.ID}</p>
      <p><strong>目标:</strong> ${jamming['Target ID']}</p>
      <p><strong>干扰方法:</strong> ${jamming.Method}</p>
      <p><strong>优先级:</strong> ${jamming.Priority}</p>
      <p><strong>时间:</strong> ${jamming.Time || jamming['Release Time'] || '未知'}</p>
      ${jamming['Power(dBW)'] ? `<p><strong>功率:</strong> ${jamming['Power(dBW)']} dBW</p>` : ''}
      ${jamming['Central Frequency(GHz)'] ? `<p><strong>中心频率:</strong> ${jamming['Central Frequency(GHz)']} GHz</p>` : ''}
      ${jamming['Beam Width(MHz)'] ? `<p><strong>波束宽度:</strong> ${jamming['Beam Width(MHz)']} MHz</p>` : ''}
      ${jamming['Beam Width(GHz)'] ? `<p><strong>波束宽度:</strong> ${jamming['Beam Width(GHz)']} GHz</p>` : ''}
      ${jamming['Decoy Type'] ? `<p><strong>诱饵类型:</strong> ${jamming['Decoy Type']}</p>` : ''}
    `
  }
}
