/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.98
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Matrix2-7dfd434a","./ComponentDatatype-9b23164a","./defaultValue-50f7432c","./EllipsoidRhumbLine-5454653c","./GeometryAttribute-4d82fade","./WebGLConstants-58abc51a"],(function(e,t,n,r,a,i,o){"use strict";var s={exports:{}};function u(e,t,n){n=n||2;var r,a,i,o,s,u,p,h=t&&t.length,y=h?t[0]*n:e.length,c=x(e,0,y,n,!0),f=[];if(!c||c.next===c.prev)return f;if(h&&(c=function(e,t,n,r){var a,i,o,s=[];for(a=0,i=t.length;a<i;a++)(o=x(e,t[a]*r,a<i-1?t[a+1]*r:e.length,r,!1))===o.next&&(o.steiner=!0),s.push(g(o));for(s.sort(v),a=0;a<s.length;a++)n=d(s[a],n);return n}(e,t,c,n)),e.length>80*n){r=i=e[0],a=o=e[1];for(var m=n;m<y;m+=n)(s=e[m])<r&&(r=s),(u=e[m+1])<a&&(a=u),s>i&&(i=s),u>o&&(o=u);p=0!==(p=Math.max(i-r,o-a))?32767/p:0}return l(c,f,n,r,a,p,0),f}function x(e,t,n,r,a){var i,o;if(a===O(e,t,n,r)>0)for(i=t;i<n;i+=r)o=D(i,e[i],e[i+1],o);else for(i=n-r;i>=t;i-=r)o=D(i,e[i],e[i+1],o);return o&&S(o,o.next)&&(R(o),o=o.next),o}function p(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!S(r,r.next)&&0!==b(r.prev,r,r.next))r=r.next;else{if(R(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function l(e,t,n,r,a,i,o){if(e){!o&&i&&function(e,t,n,r){var a=e;do{0===a.z&&(a.z=C(a.x,a.y,t,n,r)),a.prevZ=a.prev,a.nextZ=a.next,a=a.next}while(a!==e);a.prevZ.nextZ=null,a.prevZ=null,function(e){var t,n,r,a,i,o,s,u,x=1;do{for(n=e,e=null,i=null,o=0;n;){for(o++,r=n,s=0,t=0;t<x&&(s++,r=r.nextZ);t++);for(u=x;s>0||u>0&&r;)0!==s&&(0===u||!r||n.z<=r.z)?(a=n,n=n.nextZ,s--):(a=r,r=r.nextZ,u--),i?i.nextZ=a:e=a,a.prevZ=i,i=a;n=r}i.nextZ=null,x*=2}while(o>1)}(a)}(e,r,a,i);for(var s,u,x=e;e.prev!==e.next;)if(s=e.prev,u=e.next,i?y(e,r,a,i):h(e))t.push(s.i/n|0),t.push(e.i/n|0),t.push(u.i/n|0),R(e),e=u.next,x=u.next;else if((e=u)===x){o?1===o?l(e=c(p(e),t,n),t,n,r,a,i,2):2===o&&f(e,t,n,r,a,i):l(p(e),t,n,r,a,i,1);break}}}function h(e){var t=e.prev,n=e,r=e.next;if(b(t,n,r)>=0)return!1;for(var a=t.x,i=n.x,o=r.x,s=t.y,u=n.y,x=r.y,p=a<i?a<o?a:o:i<o?i:o,l=s<u?s<x?s:x:u<x?u:x,h=a>i?a>o?a:o:i>o?i:o,y=s>u?s>x?s:x:u>x?u:x,c=r.next;c!==t;){if(c.x>=p&&c.x<=h&&c.y>=l&&c.y<=y&&w(a,s,i,u,o,x,c.x,c.y)&&b(c.prev,c,c.next)>=0)return!1;c=c.next}return!0}function y(e,t,n,r){var a=e.prev,i=e,o=e.next;if(b(a,i,o)>=0)return!1;for(var s=a.x,u=i.x,x=o.x,p=a.y,l=i.y,h=o.y,y=s<u?s<x?s:x:u<x?u:x,c=p<l?p<h?p:h:l<h?l:h,f=s>u?s>x?s:x:u>x?u:x,v=p>l?p>h?p:h:l>h?l:h,d=C(y,c,t,n,r),m=C(f,v,t,n,r),g=e.prevZ,A=e.nextZ;g&&g.z>=d&&A&&A.z<=m;){if(g.x>=y&&g.x<=f&&g.y>=c&&g.y<=v&&g!==a&&g!==o&&w(s,p,u,l,x,h,g.x,g.y)&&b(g.prev,g,g.next)>=0)return!1;if(g=g.prevZ,A.x>=y&&A.x<=f&&A.y>=c&&A.y<=v&&A!==a&&A!==o&&w(s,p,u,l,x,h,A.x,A.y)&&b(A.prev,A,A.next)>=0)return!1;A=A.nextZ}for(;g&&g.z>=d;){if(g.x>=y&&g.x<=f&&g.y>=c&&g.y<=v&&g!==a&&g!==o&&w(s,p,u,l,x,h,g.x,g.y)&&b(g.prev,g,g.next)>=0)return!1;g=g.prevZ}for(;A&&A.z<=m;){if(A.x>=y&&A.x<=f&&A.y>=c&&A.y<=v&&A!==a&&A!==o&&w(s,p,u,l,x,h,A.x,A.y)&&b(A.prev,A,A.next)>=0)return!1;A=A.nextZ}return!0}function c(e,t,n){var r=e;do{var a=r.prev,i=r.next.next;!S(a,i)&&E(a,r,r.next,i)&&z(a,i)&&z(i,a)&&(t.push(a.i/n|0),t.push(r.i/n|0),t.push(i.i/n|0),R(r),R(r.next),r=e=i),r=r.next}while(r!==e);return p(r)}function f(e,t,n,r,a,i){var o=e;do{for(var s=o.next.next;s!==o.prev;){if(o.i!==s.i&&A(o,s)){var u=L(o,s);return o=p(o,o.next),u=p(u,u.next),l(o,t,n,r,a,i,0),void l(u,t,n,r,a,i,0)}s=s.next}o=o.next}while(o!==e)}function v(e,t){return e.x-t.x}function d(e,t){var n=function(e,t){var n,r=t,a=e.x,i=e.y,o=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var s=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(s<=a&&s>o&&(o=s,n=r.x<r.next.x?r:r.next,s===a))return n}r=r.next}while(r!==t);if(!n)return null;var u,x=n,p=n.x,l=n.y,h=1/0;r=n;do{a>=r.x&&r.x>=p&&a!==r.x&&w(i<l?a:o,i,p,l,i<l?o:a,i,r.x,r.y)&&(u=Math.abs(i-r.y)/(a-r.x),z(r,e)&&(u<h||u===h&&(r.x>n.x||r.x===n.x&&m(n,r)))&&(n=r,h=u)),r=r.next}while(r!==x);return n}(e,t);if(!n)return t;var r=L(n,e);return p(r,r.next),p(n,n.next)}function m(e,t){return b(e.prev,e,t.prev)<0&&b(t.next,e,e.next)<0}function C(e,t,n,r,a){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*a|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*a|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function g(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function w(e,t,n,r,a,i,o,s){return(a-o)*(t-s)>=(e-o)*(i-s)&&(e-o)*(r-s)>=(n-o)*(t-s)&&(n-o)*(i-s)>=(a-o)*(r-s)}function A(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&E(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(z(e,t)&&z(t,e)&&function(e,t){var n=e,r=!1,a=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&a<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(b(e.prev,e,t.prev)||b(e,t.prev,t))||S(e,t)&&b(e.prev,e,e.next)>0&&b(t.prev,t,t.next)>0)}function b(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function S(e,t){return e.x===t.x&&e.y===t.y}function E(e,t,n,r){var a=Z(b(e,t,n)),i=Z(b(e,t,r)),o=Z(b(n,r,e)),s=Z(b(n,r,t));return a!==i&&o!==s||(!(0!==a||!M(e,n,t))||(!(0!==i||!M(e,r,t))||(!(0!==o||!M(n,e,r))||!(0!==s||!M(n,t,r)))))}function M(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function Z(e){return e>0?1:e<0?-1:0}function z(e,t){return b(e.prev,e,e.next)<0?b(e,t,e.next)>=0&&b(e,e.prev,t)>=0:b(e,t,e.prev)<0||b(e,e.next,t)<0}function L(e,t){var n=new G(e.i,e.x,e.y),r=new G(t.i,t.x,t.y),a=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=a,a.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function D(e,t,n,r){var a=new G(e,t,n);return r?(a.next=r.next,a.prev=r,r.next.prev=a,r.next=a):(a.prev=a,a.next=a),a}function R(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function G(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function O(e,t,n,r){for(var a=0,i=t,o=n-r;i<n;i+=r)a+=(e[o]-e[i])*(e[i+1]+e[o+1]),o=i;return a}s.exports=u,s.exports.default=u,u.deviation=function(e,t,n,r){var a=t&&t.length,i=a?t[0]*n:e.length,o=Math.abs(O(e,0,i,n));if(a)for(var s=0,u=t.length;s<u;s++){var x=t[s]*n,p=s<u-1?t[s+1]*n:e.length;o-=Math.abs(O(e,x,p,n))}var l=0;for(s=0;s<r.length;s+=3){var h=r[s]*n,y=r[s+1]*n,c=r[s+2]*n;l+=Math.abs((e[h]-e[c])*(e[y+1]-e[h+1])-(e[h]-e[y])*(e[c+1]-e[h+1]))}return 0===o&&0===l?0:Math.abs((l-o)/o)},u.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,a=0;a<e.length;a++){for(var i=0;i<e[a].length;i++)for(var o=0;o<t;o++)n.vertices.push(e[a][i][o]);a>0&&(r+=e[a-1].length,n.holes.push(r))}return n};const T={CLOCKWISE:o.WebGLConstants.CW,COUNTER_CLOCKWISE:o.WebGLConstants.CCW,validate:function(e){return e===T.CLOCKWISE||e===T.COUNTER_CLOCKWISE}};var B=Object.freeze(T);const W=new t.Cartesian3,P=new t.Cartesian3,$={computeArea2D:function(e){const t=e.length;let n=0;for(let r=t-1,a=0;a<t;r=a++){const t=e[r],i=e[a];n+=t.x*i.y-i.x*t.y}return.5*n},computeWindingOrder2D:function(e){return $.computeArea2D(e)>0?B.COUNTER_CLOCKWISE:B.CLOCKWISE},triangulate:function(e,n){const r=t.Cartesian2.packArray(e);return s.exports(r,n,2)}},I=new t.Cartesian3,N=new t.Cartesian3,U=new t.Cartesian3,_=new t.Cartesian3,K=new t.Cartesian3,V=new t.Cartesian3,F=new t.Cartesian3,k=new t.Cartesian2,q=new t.Cartesian2,j=new t.Cartesian2,H=new t.Cartesian2;$.computeSubdivision=function(e,a,o,s,u){u=r.defaultValue(u,n.CesiumMath.RADIANS_PER_DEGREE);const x=r.defined(s),p=o.slice(0);let l;const h=a.length,y=new Array(3*h),c=new Array(2*h);let f=0,v=0;for(l=0;l<h;l++){const e=a[l];if(y[f++]=e.x,y[f++]=e.y,y[f++]=e.z,x){const e=s[l];c[v++]=e.x,c[v++]=e.y}}const d=[],m={},C=e.maximumRadius,g=n.CesiumMath.chordLength(u,C),w=g*g;for(;p.length>0;){const e=p.pop(),n=p.pop(),a=p.pop(),i=t.Cartesian3.fromArray(y,3*a,I),o=t.Cartesian3.fromArray(y,3*n,N),s=t.Cartesian3.fromArray(y,3*e,U);let u,h,f;x&&(u=t.Cartesian2.fromArray(c,2*a,k),h=t.Cartesian2.fromArray(c,2*n,q),f=t.Cartesian2.fromArray(c,2*e,j));const v=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(i,_),C,_),g=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(o,K),C,K),A=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(s,V),C,V),b=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(v,g,F)),S=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(g,A,F)),E=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(A,v,F)),M=Math.max(b,S,E);let Z,z,L;M>w?b===M?(Z=`${Math.min(a,n)} ${Math.max(a,n)}`,l=m[Z],r.defined(l)||(z=t.Cartesian3.add(i,o,F),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(u,h,H),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),p.push(a,l,e),p.push(l,n,e)):S===M?(Z=`${Math.min(n,e)} ${Math.max(n,e)}`,l=m[Z],r.defined(l)||(z=t.Cartesian3.add(o,s,F),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(h,f,H),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),p.push(n,l,a),p.push(l,e,a)):E===M&&(Z=`${Math.min(e,a)} ${Math.max(e,a)}`,l=m[Z],r.defined(l)||(z=t.Cartesian3.add(s,i,F),t.Cartesian3.multiplyByScalar(z,.5,z),y.push(z.x,z.y,z.z),l=y.length/3-1,m[Z]=l,x&&(L=t.Cartesian2.add(f,u,H),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),p.push(e,l,n),p.push(l,a,n)):(d.push(a),d.push(n),d.push(e))}const A={attributes:{position:new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y})},indices:d,primitiveType:i.PrimitiveType.TRIANGLES};return x&&(A.attributes.st=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:c})),new i.Geometry(A)};const J=new t.Cartographic,Q=new t.Cartographic,X=new t.Cartographic,Y=new t.Cartographic;$.computeRhumbLineSubdivision=function(e,o,s,u,x){x=r.defaultValue(x,n.CesiumMath.RADIANS_PER_DEGREE);const p=r.defined(u),l=s.slice(0);let h;const y=o.length,c=new Array(3*y),f=new Array(2*y);let v=0,d=0;for(h=0;h<y;h++){const e=o[h];if(c[v++]=e.x,c[v++]=e.y,c[v++]=e.z,p){const e=u[h];f[d++]=e.x,f[d++]=e.y}}const m=[],C={},g=e.maximumRadius,w=n.CesiumMath.chordLength(x,g),A=new a.EllipsoidRhumbLine(void 0,void 0,e),b=new a.EllipsoidRhumbLine(void 0,void 0,e),S=new a.EllipsoidRhumbLine(void 0,void 0,e);for(;l.length>0;){const n=l.pop(),a=l.pop(),i=l.pop(),o=t.Cartesian3.fromArray(c,3*i,I),s=t.Cartesian3.fromArray(c,3*a,N),u=t.Cartesian3.fromArray(c,3*n,U);let x,y,v;p&&(x=t.Cartesian2.fromArray(f,2*i,k),y=t.Cartesian2.fromArray(f,2*a,q),v=t.Cartesian2.fromArray(f,2*n,j));const d=e.cartesianToCartographic(o,J),g=e.cartesianToCartographic(s,Q),E=e.cartesianToCartographic(u,X);A.setEndPoints(d,g);const M=A.surfaceDistance;b.setEndPoints(g,E);const Z=b.surfaceDistance;S.setEndPoints(E,d);const z=S.surfaceDistance,L=Math.max(M,Z,z);let D,R,G,O,T;L>w?M===L?(D=`${Math.min(i,a)} ${Math.max(i,a)}`,h=C[D],r.defined(h)||(R=A.interpolateUsingFraction(.5,Y),G=.5*(d.height+g.height),O=t.Cartesian3.fromRadians(R.longitude,R.latitude,G,e,F),c.push(O.x,O.y,O.z),h=c.length/3-1,C[D]=h,p&&(T=t.Cartesian2.add(x,y,H),t.Cartesian2.multiplyByScalar(T,.5,T),f.push(T.x,T.y))),l.push(i,h,n),l.push(h,a,n)):Z===L?(D=`${Math.min(a,n)} ${Math.max(a,n)}`,h=C[D],r.defined(h)||(R=b.interpolateUsingFraction(.5,Y),G=.5*(g.height+E.height),O=t.Cartesian3.fromRadians(R.longitude,R.latitude,G,e,F),c.push(O.x,O.y,O.z),h=c.length/3-1,C[D]=h,p&&(T=t.Cartesian2.add(y,v,H),t.Cartesian2.multiplyByScalar(T,.5,T),f.push(T.x,T.y))),l.push(a,h,i),l.push(h,n,i)):z===L&&(D=`${Math.min(n,i)} ${Math.max(n,i)}`,h=C[D],r.defined(h)||(R=S.interpolateUsingFraction(.5,Y),G=.5*(E.height+d.height),O=t.Cartesian3.fromRadians(R.longitude,R.latitude,G,e,F),c.push(O.x,O.y,O.z),h=c.length/3-1,C[D]=h,p&&(T=t.Cartesian2.add(v,x,H),t.Cartesian2.multiplyByScalar(T,.5,T),f.push(T.x,T.y))),l.push(n,h,a),l.push(h,i,a)):(m.push(i),m.push(a),m.push(n))}const E={attributes:{position:new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:m,primitiveType:i.PrimitiveType.TRIANGLES};return p&&(E.attributes.st=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:f})),new i.Geometry(E)},$.scaleToGeodeticHeight=function(e,n,a,i){a=r.defaultValue(a,t.Ellipsoid.WGS84);let o=W,s=P;if(n=r.defaultValue(n,0),i=r.defaultValue(i,!0),r.defined(e)){const r=e.length;for(let u=0;u<r;u+=3)t.Cartesian3.fromArray(e,u,s),i&&(s=a.scaleToGeodeticSurface(s,s)),0!==n&&(o=a.geodeticSurfaceNormal(s,o),t.Cartesian3.multiplyByScalar(o,n,o),t.Cartesian3.add(s,o,s)),e[u]=s.x,e[u+1]=s.y,e[u+2]=s.z}return e};var ee=$;e.PolygonPipeline=ee,e.WindingOrder=B}));
