{"name": "gongs-project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint --fix", "electron:build": "vue-cli-service electron:build", "electron:serve": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "background.js", "dependencies": {"axios": "^1.10.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/eslint-parser": "^7.27.5", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "electron": "^13.0.0", "electron-devtools-installer": "^3.1.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^8.7.1", "eslint-webpack-plugin": "^5.0.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "vue-cli-plugin-electron-builder": "~2.1.1", "vue-eslint-parser": "^8.0.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}