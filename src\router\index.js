/*
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2025-07-16 16:53:05
 * @LastEditors: tzs
 * @LastEditTime: 2025-07-17 09:14:10
 *
 */
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/route/1' // 默认重定向到第一个路由
  },
  // 11个功能相同的路由，只是名称不同
  {
    path: '/route/1',
    name: '行动策略模型',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/2',
    name: '2',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/3',
    name: '3',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/4',
    name: '4',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/5',
    name: '5',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/6',
    name: '6',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/7',
    name: '7',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/8',
    name: '8',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/9',
    name: '9',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/10',
    name: '10',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  },
  {
    path: '/route/11',
    name: '11',
    component: resolve => require(['@/views/CommonPage.vue'], resolve)
  }
]

const router = new VueRouter({
  routes
})

export default router
