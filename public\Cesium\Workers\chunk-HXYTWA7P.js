/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.113
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as qe}from"./chunk-AT27Z3WO.js";import{a as xt,b as L,c as vt}from"./chunk-HB5KYIAZ.js";import{a as f,b as De,c as sn,d as ze,e as B}from"./chunk-T4TQMW7B.js";import{a as J}from"./chunk-7TT2TZHW.js";import{a as Ee}from"./chunk-OHNTPGT4.js";import{a as O}from"./chunk-XQTMMRVI.js";import{a as R,b as w}from"./chunk-QHDGFGBI.js";import{a as _t,c as St,d as Ge,e as d}from"./chunk-MVZBAA6W.js";var or=St((et,tt)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var t=typeof et=="object"&&et&&!et.nodeType&&et,n=typeof tt=="object"&&tt&&!tt.nodeType&&tt,i=typeof global=="object"&&global;(i.global===i||i.window===i||i.self===i)&&(e=i);var s,o=**********,c=36,u=1,p=26,b=38,g=700,y=72,S=128,A="-",I=/^xn--/,T=/[^\x20-\x7E]/,k=/[\x2E\u3002\uFF0E\uFF61]/g,D={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},F=c-u,z=Math.floor,M=String.fromCharCode,N;function Y(h){throw new RangeError(D[h])}function oe(h,P){for(var q=h.length,V=[];q--;)V[q]=P(h[q]);return V}function ie(h,P){var q=h.split("@"),V="";q.length>1&&(V=q[0]+"@",h=q[1]),h=h.replace(k,".");var ne=h.split("."),se=oe(ne,P).join(".");return V+se}function X(h){for(var P=[],q=0,V=h.length,ne,se;q<V;)ne=h.charCodeAt(q++),ne>=55296&&ne<=56319&&q<V?(se=h.charCodeAt(q++),(se&64512)==56320?P.push(((ne&1023)<<10)+(se&1023)+65536):(P.push(ne),q--)):P.push(ne);return P}function W(h){return oe(h,function(P){var q="";return P>65535&&(P-=65536,q+=M(P>>>10&1023|55296),P=56320|P&1023),q+=M(P),q}).join("")}function Q(h){return h-48<10?h-22:h-65<26?h-65:h-97<26?h-97:c}function r(h,P){return h+22+75*(h<26)-((P!=0)<<5)}function a(h,P,q){var V=0;for(h=q?z(h/g):h>>1,h+=z(h/P);h>F*p>>1;V+=c)h=z(h/F);return z(V+(F+1)*h/(h+b))}function l(h){var P=[],q=h.length,V,ne=0,se=S,G=y,ae,he,be,ye,fe,_e,xe,Ue,Le;for(ae=h.lastIndexOf(A),ae<0&&(ae=0),he=0;he<ae;++he)h.charCodeAt(he)>=128&&Y("not-basic"),P.push(h.charCodeAt(he));for(be=ae>0?ae+1:0;be<q;){for(ye=ne,fe=1,_e=c;be>=q&&Y("invalid-input"),xe=Q(h.charCodeAt(be++)),(xe>=c||xe>z((o-ne)/fe))&&Y("overflow"),ne+=xe*fe,Ue=_e<=G?u:_e>=G+p?p:_e-G,!(xe<Ue);_e+=c)Le=c-Ue,fe>z(o/Le)&&Y("overflow"),fe*=Le;V=P.length+1,G=a(ne-ye,V,ye==0),z(ne/V)>o-se&&Y("overflow"),se+=z(ne/V),ne%=V,P.splice(ne++,0,se)}return W(P)}function m(h){var P,q,V,ne,se,G,ae,he,be,ye,fe,_e=[],xe,Ue,Le,on;for(h=X(h),xe=h.length,P=S,q=0,se=y,G=0;G<xe;++G)fe=h[G],fe<128&&_e.push(M(fe));for(V=ne=_e.length,ne&&_e.push(A);V<xe;){for(ae=o,G=0;G<xe;++G)fe=h[G],fe>=P&&fe<ae&&(ae=fe);for(Ue=V+1,ae-P>z((o-q)/Ue)&&Y("overflow"),q+=(ae-P)*Ue,P=ae,G=0;G<xe;++G)if(fe=h[G],fe<P&&++q>o&&Y("overflow"),fe==P){for(he=q,be=c;ye=be<=se?u:be>=se+p?p:be-se,!(he<ye);be+=c)on=he-ye,Le=c-ye,_e.push(M(r(ye+on%Le,0))),he=z(on/Le);_e.push(M(r(he,0))),se=a(q,Ue,V==ne),q=0,++V}++q,++P}return _e.join("")}function _(h){return ie(h,function(P){return I.test(P)?l(P.slice(4).toLowerCase()):P})}function v(h){return ie(h,function(P){return T.test(P)?"xn--"+m(P):P})}if(s={version:"1.3.2",ucs2:{decode:X,encode:W},decode:l,encode:m,toASCII:v,toUnicode:_},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return s});else if(t&&n)if(tt.exports==t)n.exports=s;else for(N in s)s.hasOwnProperty(N)&&(t[N]=s[N]);else e.punycode=s})(et)});var sr=St((ir,It)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof It=="object"&&It.exports?It.exports=t():typeof define=="function"&&define.amd?define(t):e.IPv6=t(e)})(ir,function(e){"use strict";var t=e&&e.IPv6;function n(s){var o=s.toLowerCase(),c=o.split(":"),u=c.length,p=8;c[0]===""&&c[1]===""&&c[2]===""?(c.shift(),c.shift()):c[0]===""&&c[1]===""?c.shift():c[u-1]===""&&c[u-2]===""&&c.pop(),u=c.length,c[u-1].indexOf(".")!==-1&&(p=7);var b;for(b=0;b<u&&c[b]!=="";b++);if(b<p)for(c.splice(b,1,"0000");c.length<p;)c.splice(b,0,"0000");for(var g,y=0;y<p;y++){g=c[y].split("");for(var S=0;S<3&&(g[0]==="0"&&g.length>1);S++)g.splice(0,1);c[y]=g.join("")}var A=-1,I=0,T=0,k=-1,D=!1;for(y=0;y<p;y++)D?c[y]==="0"?T+=1:(D=!1,T>I&&(A=k,I=T)):c[y]==="0"&&(D=!0,k=y,T=1);T>I&&(A=k,I=T),I>1&&c.splice(A,I,""),u=c.length;var F="";for(c[0]===""&&(F=":"),y=0;y<u&&(F+=c[y],y!==u-1);y++)F+=":";return c[u-1]===""&&(F+=":"),F}function i(){return e.IPv6===this&&(e.IPv6=t),this}return{best:n,noConflict:i}})});var cr=St((ar,Ut)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Ut=="object"&&Ut.exports?Ut.exports=t():typeof define=="function"&&define.amd?define(t):e.SecondLevelDomains=t(e)})(ar,function(e){"use strict";var t=e&&e.SecondLevelDomains,n={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return!1;var o=i.lastIndexOf(".",s-1);if(o<=0||o>=s-1)return!1;var c=n.list[i.slice(s+1)];return c?c.indexOf(" "+i.slice(o+1,s)+" ")>=0:!1},is:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return!1;var o=i.lastIndexOf(".",s-1);if(o>=0)return!1;var c=n.list[i.slice(s+1)];return c?c.indexOf(" "+i.slice(0,s)+" ")>=0:!1},get:function(i){var s=i.lastIndexOf(".");if(s<=0||s>=i.length-1)return null;var o=i.lastIndexOf(".",s-1);if(o<=0||o>=s-1)return null;var c=n.list[i.slice(s+1)];return!c||c.indexOf(" "+i.slice(o+1,s)+" ")<0?null:i.slice(o+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return n})});var $e=St((ur,Dt)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,t){"use strict";typeof Dt=="object"&&Dt.exports?Dt.exports=t(or(),sr(),cr()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],t):e.URI=t(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(ur,function(e,t,n,i){"use strict";var s=i&&i.URI;function o(r,a){var l=arguments.length>=1,m=arguments.length>=2;if(!(this instanceof o))return l?m?new o(r,a):new o(r):new o;if(r===void 0){if(l)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?r=location.href+"":r=""}if(r===null&&l)throw new TypeError("null is not a valid argument for URI");return this.href(r),a!==void 0?this.absoluteTo(a):this}function c(r){return/^[0-9]+$/.test(r)}o.version="1.19.11";var u=o.prototype,p=Object.prototype.hasOwnProperty;function b(r){return r.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function g(r){return r===void 0?"Undefined":String(Object.prototype.toString.call(r)).slice(8,-1)}function y(r){return g(r)==="Array"}function S(r,a){var l={},m,_;if(g(a)==="RegExp")l=null;else if(y(a))for(m=0,_=a.length;m<_;m++)l[a[m]]=!0;else l[a]=!0;for(m=0,_=r.length;m<_;m++){var v=l&&l[r[m]]!==void 0||!l&&a.test(r[m]);v&&(r.splice(m,1),_--,m--)}return r}function A(r,a){var l,m;if(y(a)){for(l=0,m=a.length;l<m;l++)if(!A(r,a[l]))return!1;return!0}var _=g(a);for(l=0,m=r.length;l<m;l++)if(_==="RegExp"){if(typeof r[l]=="string"&&r[l].match(a))return!0}else if(r[l]===a)return!0;return!1}function I(r,a){if(!y(r)||!y(a)||r.length!==a.length)return!1;r.sort(),a.sort();for(var l=0,m=r.length;l<m;l++)if(r[l]!==a[l])return!1;return!0}function T(r){var a=/^\/+|\/+$/g;return r.replace(a,"")}o._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:o.preventInvalidHostname,duplicateQueryParameters:o.duplicateQueryParameters,escapeQuerySpace:o.escapeQuerySpace}},o.preventInvalidHostname=!1,o.duplicateQueryParameters=!1,o.escapeQuerySpace=!0,o.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,o.idn_expression=/[^a-z0-9\._-]/i,o.punycode_expression=/(xn--)/i,o.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,o.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,o.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,o.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},o.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,o.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},o.hostProtocols=["http","https"],o.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,o.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},o.getDomAttribute=function(r){if(!(!r||!r.nodeName)){var a=r.nodeName.toLowerCase();if(!(a==="input"&&r.type!=="image"))return o.domAttributes[a]}};function k(r){return escape(r)}function D(r){return encodeURIComponent(r).replace(/[!'()*]/g,k).replace(/\*/g,"%2A")}o.encode=D,o.decode=decodeURIComponent,o.iso8859=function(){o.encode=escape,o.decode=unescape},o.unicode=function(){o.encode=D,o.decode=decodeURIComponent},o.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},o.encodeQuery=function(r,a){var l=o.encode(r+"");return a===void 0&&(a=o.escapeQuerySpace),a?l.replace(/%20/g,"+"):l},o.decodeQuery=function(r,a){r+="",a===void 0&&(a=o.escapeQuerySpace);try{return o.decode(a?r.replace(/\+/g,"%20"):r)}catch{return r}};var F={encode:"encode",decode:"decode"},z,M=function(r,a){return function(l){try{return o[a](l+"").replace(o.characters[r][a].expression,function(m){return o.characters[r][a].map[m]})}catch{return l}}};for(z in F)o[z+"PathSegment"]=M("pathname",F[z]),o[z+"UrnPathSegment"]=M("urnpath",F[z]);var N=function(r,a,l){return function(m){var _;l?_=function(q){return o[a](o[l](q))}:_=o[a];for(var v=(m+"").split(r),h=0,P=v.length;h<P;h++)v[h]=_(v[h]);return v.join(r)}};o.decodePath=N("/","decodePathSegment"),o.decodeUrnPath=N(":","decodeUrnPathSegment"),o.recodePath=N("/","encodePathSegment","decode"),o.recodeUrnPath=N(":","encodeUrnPathSegment","decode"),o.encodeReserved=M("reserved","encode"),o.parse=function(r,a){var l;return a||(a={preventInvalidHostname:o.preventInvalidHostname}),r=r.replace(o.leading_whitespace_expression,""),r=r.replace(o.ascii_tab_whitespace,""),l=r.indexOf("#"),l>-1&&(a.fragment=r.substring(l+1)||null,r=r.substring(0,l)),l=r.indexOf("?"),l>-1&&(a.query=r.substring(l+1)||null,r=r.substring(0,l)),r=r.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),r=r.replace(/^[/\\]{2,}/i,"//"),r.substring(0,2)==="//"?(a.protocol=null,r=r.substring(2),r=o.parseAuthority(r,a)):(l=r.indexOf(":"),l>-1&&(a.protocol=r.substring(0,l)||null,a.protocol&&!a.protocol.match(o.protocol_expression)?a.protocol=void 0:r.substring(l+1,l+3).replace(/\\/g,"/")==="//"?(r=r.substring(l+3),r=o.parseAuthority(r,a)):(r=r.substring(l+1),a.urn=!0))),a.path=r,a},o.parseHost=function(r,a){r||(r=""),r=r.replace(/\\/g,"/");var l=r.indexOf("/"),m,_;if(l===-1&&(l=r.length),r.charAt(0)==="[")m=r.indexOf("]"),a.hostname=r.substring(1,m)||null,a.port=r.substring(m+2,l)||null,a.port==="/"&&(a.port=null);else{var v=r.indexOf(":"),h=r.indexOf("/"),P=r.indexOf(":",v+1);P!==-1&&(h===-1||P<h)?(a.hostname=r.substring(0,l)||null,a.port=null):(_=r.substring(0,l).split(":"),a.hostname=_[0]||null,a.port=_[1]||null)}return a.hostname&&r.substring(l).charAt(0)!=="/"&&(l++,r="/"+r),a.preventInvalidHostname&&o.ensureValidHostname(a.hostname,a.protocol),a.port&&o.ensureValidPort(a.port),r.substring(l)||"/"},o.parseAuthority=function(r,a){return r=o.parseUserinfo(r,a),o.parseHost(r,a)},o.parseUserinfo=function(r,a){var l=r,m=r.indexOf("\\");m!==-1&&(r=r.replace(/\\/g,"/"));var _=r.indexOf("/"),v=r.lastIndexOf("@",_>-1?_:r.length-1),h;return v>-1&&(_===-1||v<_)?(h=r.substring(0,v).split(":"),a.username=h[0]?o.decode(h[0]):null,h.shift(),a.password=h[0]?o.decode(h.join(":")):null,r=l.substring(v+1)):(a.username=null,a.password=null),r},o.parseQuery=function(r,a){if(!r)return{};if(r=r.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!r)return{};for(var l={},m=r.split("&"),_=m.length,v,h,P,q=0;q<_;q++)v=m[q].split("="),h=o.decodeQuery(v.shift(),a),P=v.length?o.decodeQuery(v.join("="),a):null,h!=="__proto__"&&(p.call(l,h)?((typeof l[h]=="string"||l[h]===null)&&(l[h]=[l[h]]),l[h].push(P)):l[h]=P);return l},o.build=function(r){var a="",l=!1;return r.protocol&&(a+=r.protocol+":"),!r.urn&&(a||r.hostname)&&(a+="//",l=!0),a+=o.buildAuthority(r)||"",typeof r.path=="string"&&(r.path.charAt(0)!=="/"&&l&&(a+="/"),a+=r.path),typeof r.query=="string"&&r.query&&(a+="?"+r.query),typeof r.fragment=="string"&&r.fragment&&(a+="#"+r.fragment),a},o.buildHost=function(r){var a="";if(r.hostname)o.ip6_expression.test(r.hostname)?a+="["+r.hostname+"]":a+=r.hostname;else return"";return r.port&&(a+=":"+r.port),a},o.buildAuthority=function(r){return o.buildUserinfo(r)+o.buildHost(r)},o.buildUserinfo=function(r){var a="";return r.username&&(a+=o.encode(r.username)),r.password&&(a+=":"+o.encode(r.password)),a&&(a+="@"),a},o.buildQuery=function(r,a,l){var m="",_,v,h,P;for(v in r)if(v!=="__proto__"&&p.call(r,v))if(y(r[v]))for(_={},h=0,P=r[v].length;h<P;h++)r[v][h]!==void 0&&_[r[v][h]+""]===void 0&&(m+="&"+o.buildQueryParameter(v,r[v][h],l),a!==!0&&(_[r[v][h]+""]=!0));else r[v]!==void 0&&(m+="&"+o.buildQueryParameter(v,r[v],l));return m.substring(1)},o.buildQueryParameter=function(r,a,l){return o.encodeQuery(r,l)+(a!==null?"="+o.encodeQuery(a,l):"")},o.addQuery=function(r,a,l){if(typeof a=="object")for(var m in a)p.call(a,m)&&o.addQuery(r,m,a[m]);else if(typeof a=="string"){if(r[a]===void 0){r[a]=l;return}else typeof r[a]=="string"&&(r[a]=[r[a]]);y(l)||(l=[l]),r[a]=(r[a]||[]).concat(l)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},o.setQuery=function(r,a,l){if(typeof a=="object")for(var m in a)p.call(a,m)&&o.setQuery(r,m,a[m]);else if(typeof a=="string")r[a]=l===void 0?null:l;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},o.removeQuery=function(r,a,l){var m,_,v;if(y(a))for(m=0,_=a.length;m<_;m++)r[a[m]]=void 0;else if(g(a)==="RegExp")for(v in r)a.test(v)&&(r[v]=void 0);else if(typeof a=="object")for(v in a)p.call(a,v)&&o.removeQuery(r,v,a[v]);else if(typeof a=="string")l!==void 0?g(l)==="RegExp"?!y(r[a])&&l.test(r[a])?r[a]=void 0:r[a]=S(r[a],l):r[a]===String(l)&&(!y(l)||l.length===1)?r[a]=void 0:y(r[a])&&(r[a]=S(r[a],l)):r[a]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},o.hasQuery=function(r,a,l,m){switch(g(a)){case"String":break;case"RegExp":for(var _ in r)if(p.call(r,_)&&a.test(_)&&(l===void 0||o.hasQuery(r,_,l)))return!0;return!1;case"Object":for(var v in a)if(p.call(a,v)&&!o.hasQuery(r,v,a[v]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(g(l)){case"Undefined":return a in r;case"Boolean":var h=!!(y(r[a])?r[a].length:r[a]);return l===h;case"Function":return!!l(r[a],a,r);case"Array":if(!y(r[a]))return!1;var P=m?A:I;return P(r[a],l);case"RegExp":return y(r[a])?m?A(r[a],l):!1:!!(r[a]&&r[a].match(l));case"Number":l=String(l);case"String":return y(r[a])?m?A(r[a],l):!1:r[a]===l;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},o.joinPaths=function(){for(var r=[],a=[],l=0,m=0;m<arguments.length;m++){var _=new o(arguments[m]);r.push(_);for(var v=_.segment(),h=0;h<v.length;h++)typeof v[h]=="string"&&a.push(v[h]),v[h]&&l++}if(!a.length||!l)return new o("");var P=new o("").segment(a);return(r[0].path()===""||r[0].path().slice(0,1)==="/")&&P.path("/"+P.path()),P.normalize()},o.commonPath=function(r,a){var l=Math.min(r.length,a.length),m;for(m=0;m<l;m++)if(r.charAt(m)!==a.charAt(m)){m--;break}return m<1?r.charAt(0)===a.charAt(0)&&r.charAt(0)==="/"?"/":"":((r.charAt(m)!=="/"||a.charAt(m)!=="/")&&(m=r.substring(0,m).lastIndexOf("/")),r.substring(0,m+1))},o.withinString=function(r,a,l){l||(l={});var m=l.start||o.findUri.start,_=l.end||o.findUri.end,v=l.trim||o.findUri.trim,h=l.parens||o.findUri.parens,P=/[a-z0-9-]=["']?$/i;for(m.lastIndex=0;;){var q=m.exec(r);if(!q)break;var V=q.index;if(l.ignoreHtml){var ne=r.slice(Math.max(V-3,0),V);if(ne&&P.test(ne))continue}for(var se=V+r.slice(V).search(_),G=r.slice(V,se),ae=-1;;){var he=h.exec(G);if(!he)break;var be=he.index+he[0].length;ae=Math.max(ae,be)}if(ae>-1?G=G.slice(0,ae)+G.slice(ae).replace(v,""):G=G.replace(v,""),!(G.length<=q[0].length)&&!(l.ignore&&l.ignore.test(G))){se=V+G.length;var ye=a(G,V,se,r);if(ye===void 0){m.lastIndex=se;continue}ye=String(ye),r=r.slice(0,V)+ye+r.slice(se),m.lastIndex=V+ye.length}}return m.lastIndex=0,r},o.ensureValidHostname=function(r,a){var l=!!r,m=!!a,_=!1;if(m&&(_=A(o.hostProtocols,a)),_&&!l)throw new TypeError("Hostname cannot be empty, if protocol is "+a);if(r&&r.match(o.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(r).match(o.invalid_hostname_characters))throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-:_]')}},o.ensureValidPort=function(r){if(r){var a=Number(r);if(!(c(a)&&a>0&&a<65536))throw new TypeError('Port "'+r+'" is not a valid port')}},o.noConflict=function(r){if(r){var a={URI:this.noConflict()};return i.URITemplate&&typeof i.URITemplate.noConflict=="function"&&(a.URITemplate=i.URITemplate.noConflict()),i.IPv6&&typeof i.IPv6.noConflict=="function"&&(a.IPv6=i.IPv6.noConflict()),i.SecondLevelDomains&&typeof i.SecondLevelDomains.noConflict=="function"&&(a.SecondLevelDomains=i.SecondLevelDomains.noConflict()),a}else i.URI===this&&(i.URI=s);return this},u.build=function(r){return r===!0?this._deferred_build=!0:(r===void 0||this._deferred_build)&&(this._string=o.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new o(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function Y(r){return function(a,l){return a===void 0?this._parts[r]||"":(this._parts[r]=a||null,this.build(!l),this)}}function oe(r,a){return function(l,m){return l===void 0?this._parts[r]||"":(l!==null&&(l=l+"",l.charAt(0)===a&&(l=l.substring(1))),this._parts[r]=l,this.build(!m),this)}}u.protocol=Y("protocol"),u.username=Y("username"),u.password=Y("password"),u.hostname=Y("hostname"),u.port=Y("port"),u.query=oe("query","?"),u.fragment=oe("fragment","#"),u.search=function(r,a){var l=this.query(r,a);return typeof l=="string"&&l.length?"?"+l:l},u.hash=function(r,a){var l=this.fragment(r,a);return typeof l=="string"&&l.length?"#"+l:l},u.pathname=function(r,a){if(r===void 0||r===!0){var l=this._parts.path||(this._parts.hostname?"/":"");return r?(this._parts.urn?o.decodeUrnPath:o.decodePath)(l):l}else return this._parts.urn?this._parts.path=r?o.recodeUrnPath(r):"":this._parts.path=r?o.recodePath(r):"/",this.build(!a),this},u.path=u.pathname,u.href=function(r,a){var l;if(r===void 0)return this.toString();this._string="",this._parts=o._parts();var m=r instanceof o,_=typeof r=="object"&&(r.hostname||r.path||r.pathname);if(r.nodeName){var v=o.getDomAttribute(r);r=r[v]||"",_=!1}if(!m&&_&&r.pathname!==void 0&&(r=r.toString()),typeof r=="string"||r instanceof String)this._parts=o.parse(String(r),this._parts);else if(m||_){var h=m?r._parts:r;for(l in h)l!=="query"&&p.call(this._parts,l)&&(this._parts[l]=h[l]);h.query&&this.query(h.query,!1)}else throw new TypeError("invalid input");return this.build(!a),this},u.is=function(r){var a=!1,l=!1,m=!1,_=!1,v=!1,h=!1,P=!1,q=!this._parts.urn;switch(this._parts.hostname&&(q=!1,l=o.ip4_expression.test(this._parts.hostname),m=o.ip6_expression.test(this._parts.hostname),a=l||m,_=!a,v=_&&n&&n.has(this._parts.hostname),h=_&&o.idn_expression.test(this._parts.hostname),P=_&&o.punycode_expression.test(this._parts.hostname)),r.toLowerCase()){case"relative":return q;case"absolute":return!q;case"domain":case"name":return _;case"sld":return v;case"ip":return a;case"ip4":case"ipv4":case"inet4":return l;case"ip6":case"ipv6":case"inet6":return m;case"idn":return h;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return P}return null};var ie=u.protocol,X=u.port,W=u.hostname;u.protocol=function(r,a){if(r&&(r=r.replace(/:(\/\/)?$/,""),!r.match(o.protocol_expression)))throw new TypeError('Protocol "'+r+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return ie.call(this,r,a)},u.scheme=u.protocol,u.port=function(r,a){return this._parts.urn?r===void 0?"":this:(r!==void 0&&(r===0&&(r=null),r&&(r+="",r.charAt(0)===":"&&(r=r.substring(1)),o.ensureValidPort(r))),X.call(this,r,a))},u.hostname=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r!==void 0){var l={preventInvalidHostname:this._parts.preventInvalidHostname},m=o.parseHost(r,l);if(m!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');r=l.hostname,this._parts.preventInvalidHostname&&o.ensureValidHostname(r,this._parts.protocol)}return W.call(this,r,a)},u.origin=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){var l=this.protocol(),m=this.authority();return m?(l?l+"://":"")+this.authority():""}else{var _=o(r);return this.protocol(_.protocol()).authority(_.authority()).build(!a),this}},u.host=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0)return this._parts.hostname?o.buildHost(this._parts):"";var l=o.parseHost(r,this._parts);if(l!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');return this.build(!a),this},u.authority=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0)return this._parts.hostname?o.buildAuthority(this._parts):"";var l=o.parseAuthority(r,this._parts);if(l!=="/")throw new TypeError('Hostname "'+r+'" contains characters other than [A-Z0-9.-]');return this.build(!a),this},u.userinfo=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){var l=o.buildUserinfo(this._parts);return l&&l.substring(0,l.length-1)}else return r[r.length-1]!=="@"&&(r+="@"),o.parseUserinfo(r,this._parts),this.build(!a),this},u.resource=function(r,a){var l;return r===void 0?this.path()+this.search()+this.hash():(l=o.parse(r),this._parts.path=l.path,this._parts.query=l.query,this._parts.fragment=l.fragment,this.build(!a),this)},u.subdomain=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,l)||""}else{var m=this._parts.hostname.length-this.domain().length,_=this._parts.hostname.substring(0,m),v=new RegExp("^"+b(_));if(r&&r.charAt(r.length-1)!=="."&&(r+="."),r.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return r&&o.ensureValidHostname(r,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(v,r),this.build(!a),this}},u.domain=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r=="boolean"&&(a=r,r=void 0),r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.match(/\./g);if(l&&l.length<2)return this._parts.hostname;var m=this._parts.hostname.length-this.tld(a).length-1;return m=this._parts.hostname.lastIndexOf(".",m-1)+1,this._parts.hostname.substring(m)||""}else{if(!r)throw new TypeError("cannot set domain empty");if(r.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(o.ensureValidHostname(r,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=r;else{var _=new RegExp(b(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(_,r)}return this.build(!a),this}},u.tld=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r=="boolean"&&(a=r,r=void 0),r===void 0){if(!this._parts.hostname||this.is("IP"))return"";var l=this._parts.hostname.lastIndexOf("."),m=this._parts.hostname.substring(l+1);return a!==!0&&n&&n.list[m.toLowerCase()]&&n.get(this._parts.hostname)||m}else{var _;if(r)if(r.match(/[^a-zA-Z0-9-]/))if(n&&n.is(r))_=new RegExp(b(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(_,r);else throw new TypeError('TLD "'+r+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");_=new RegExp(b(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(_,r)}else throw new TypeError("cannot set TLD empty");return this.build(!a),this}},u.directory=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0||r===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var l=this._parts.path.length-this.filename().length-1,m=this._parts.path.substring(0,l)||(this._parts.hostname?"/":"");return r?o.decodePath(m):m}else{var _=this._parts.path.length-this.filename().length,v=this._parts.path.substring(0,_),h=new RegExp("^"+b(v));return this.is("relative")||(r||(r="/"),r.charAt(0)!=="/"&&(r="/"+r)),r&&r.charAt(r.length-1)!=="/"&&(r+="/"),r=o.recodePath(r),this._parts.path=this._parts.path.replace(h,r),this.build(!a),this}},u.filename=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(typeof r!="string"){if(!this._parts.path||this._parts.path==="/")return"";var l=this._parts.path.lastIndexOf("/"),m=this._parts.path.substring(l+1);return r?o.decodePathSegment(m):m}else{var _=!1;r.charAt(0)==="/"&&(r=r.substring(1)),r.match(/\.?\//)&&(_=!0);var v=new RegExp(b(this.filename())+"$");return r=o.recodePath(r),this._parts.path=this._parts.path.replace(v,r),_?this.normalizePath(a):this.build(!a),this}},u.suffix=function(r,a){if(this._parts.urn)return r===void 0?"":this;if(r===void 0||r===!0){if(!this._parts.path||this._parts.path==="/")return"";var l=this.filename(),m=l.lastIndexOf("."),_,v;return m===-1?"":(_=l.substring(m+1),v=/^[a-z0-9%]+$/i.test(_)?_:"",r?o.decodePathSegment(v):v)}else{r.charAt(0)==="."&&(r=r.substring(1));var h=this.suffix(),P;if(h)r?P=new RegExp(b(h)+"$"):P=new RegExp(b("."+h)+"$");else{if(!r)return this;this._parts.path+="."+o.recodePath(r)}return P&&(r=o.recodePath(r),this._parts.path=this._parts.path.replace(P,r)),this.build(!a),this}},u.segment=function(r,a,l){var m=this._parts.urn?":":"/",_=this.path(),v=_.substring(0,1)==="/",h=_.split(m);if(r!==void 0&&typeof r!="number"&&(l=a,a=r,r=void 0),r!==void 0&&typeof r!="number")throw new Error('Bad segment "'+r+'", must be 0-based integer');if(v&&h.shift(),r<0&&(r=Math.max(h.length+r,0)),a===void 0)return r===void 0?h:h[r];if(r===null||h[r]===void 0)if(y(a)){h=[];for(var P=0,q=a.length;P<q;P++)!a[P].length&&(!h.length||!h[h.length-1].length)||(h.length&&!h[h.length-1].length&&h.pop(),h.push(T(a[P])))}else(a||typeof a=="string")&&(a=T(a),h[h.length-1]===""?h[h.length-1]=a:h.push(a));else a?h[r]=T(a):h.splice(r,1);return v&&h.unshift(""),this.path(h.join(m),l)},u.segmentCoded=function(r,a,l){var m,_,v;if(typeof r!="number"&&(l=a,a=r,r=void 0),a===void 0){if(m=this.segment(r,a,l),!y(m))m=m!==void 0?o.decode(m):void 0;else for(_=0,v=m.length;_<v;_++)m[_]=o.decode(m[_]);return m}if(!y(a))a=typeof a=="string"||a instanceof String?o.encode(a):a;else for(_=0,v=a.length;_<v;_++)a[_]=o.encode(a[_]);return this.segment(r,a,l)};var Q=u.query;return u.query=function(r,a){if(r===!0)return o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof r=="function"){var l=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace),m=r.call(this,l);return this._parts.query=o.buildQuery(m||l,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!a),this}else return r!==void 0&&typeof r!="string"?(this._parts.query=o.buildQuery(r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!a),this):Q.call(this,r,a)},u.setQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof r=="string"||r instanceof String)m[r]=a!==void 0?a:null;else if(typeof r=="object")for(var _ in r)p.call(r,_)&&(m[_]=r[_]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.addQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.addQuery(m,r,a===void 0?null:a),this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.removeQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.removeQuery(m,r,a),this._parts.query=o.buildQuery(m,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof r!="string"&&(l=a),this.build(!l),this},u.hasQuery=function(r,a,l){var m=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.hasQuery(m,r,a,l)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(r){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!r)),this},u.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},u.normalizePort=function(r){return typeof this._parts.protocol=="string"&&this._parts.port===o.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!r)),this},u.normalizePath=function(r){var a=this._parts.path;if(!a)return this;if(this._parts.urn)return this._parts.path=o.recodeUrnPath(this._parts.path),this.build(!r),this;if(this._parts.path==="/")return this;a=o.recodePath(a);var l,m="",_,v;for(a.charAt(0)!=="/"&&(l=!0,a="/"+a),(a.slice(-3)==="/.."||a.slice(-2)==="/.")&&(a+="/"),a=a.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),l&&(m=a.substring(1).match(/^(\.\.\/)+/)||"",m&&(m=m[0]));_=a.search(/\/\.\.(\/|$)/),_!==-1;){if(_===0){a=a.substring(3);continue}v=a.substring(0,_).lastIndexOf("/"),v===-1&&(v=_),a=a.substring(0,v)+a.substring(_+3)}return l&&this.is("relative")&&(a=m+a.substring(1)),this._parts.path=a,this.build(!r),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(r){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(o.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!r)),this},u.normalizeFragment=function(r){return this._parts.fragment||(this._parts.fragment=null,this.build(!r)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var r=o.encode,a=o.decode;o.encode=escape,o.decode=decodeURIComponent;try{this.normalize()}finally{o.encode=r,o.decode=a}return this},u.unicode=function(){var r=o.encode,a=o.decode;o.encode=D,o.decode=unescape;try{this.normalize()}finally{o.encode=r,o.decode=a}return this},u.readable=function(){var r=this.clone();r.username("").password("").normalize();var a="";if(r._parts.protocol&&(a+=r._parts.protocol+"://"),r._parts.hostname&&(r.is("punycode")&&e?(a+=e.toUnicode(r._parts.hostname),r._parts.port&&(a+=":"+r._parts.port)):a+=r.host()),r._parts.hostname&&r._parts.path&&r._parts.path.charAt(0)!=="/"&&(a+="/"),a+=r.path(!0),r._parts.query){for(var l="",m=0,_=r._parts.query.split("&"),v=_.length;m<v;m++){var h=(_[m]||"").split("=");l+="&"+o.decodeQuery(h[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),h[1]!==void 0&&(l+="="+o.decodeQuery(h[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}a+="?"+l.substring(1)}return a+=o.decodeQuery(r.hash(),!0),a},u.absoluteTo=function(r){var a=this.clone(),l=["protocol","username","password","hostname","port"],m,_,v;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(r instanceof o||(r=new o(r)),a._parts.protocol||(a._parts.protocol=r._parts.protocol,this._parts.hostname))return a;for(_=0;v=l[_];_++)a._parts[v]=r._parts[v];return a._parts.path?(a._parts.path.substring(-2)===".."&&(a._parts.path+="/"),a.path().charAt(0)!=="/"&&(m=r.directory(),m=m||(r.path().indexOf("/")===0?"/":""),a._parts.path=(m?m+"/":"")+a._parts.path,a.normalizePath())):(a._parts.path=r._parts.path,a._parts.query||(a._parts.query=r._parts.query)),a.build(),a},u.relativeTo=function(r){var a=this.clone().normalize(),l,m,_,v,h;if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(r=new o(r).normalize(),l=a._parts,m=r._parts,v=a.path(),h=r.path(),v.charAt(0)!=="/")throw new Error("URI is already relative");if(h.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(l.protocol===m.protocol&&(l.protocol=null),l.username!==m.username||l.password!==m.password||l.protocol!==null||l.username!==null||l.password!==null)return a.build();if(l.hostname===m.hostname&&l.port===m.port)l.hostname=null,l.port=null;else return a.build();if(v===h)return l.path="",a.build();if(_=o.commonPath(v,h),!_)return a.build();var P=m.path.substring(_.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return l.path=P+l.path.substring(_.length)||"./",a.build()},u.equals=function(r){var a=this.clone(),l=new o(r),m={},_={},v={},h,P,q;if(a.normalize(),l.normalize(),a.toString()===l.toString())return!0;if(h=a.query(),P=l.query(),a.query(""),l.query(""),a.toString()!==l.toString()||h.length!==P.length)return!1;m=o.parseQuery(h,this._parts.escapeQuerySpace),_=o.parseQuery(P,this._parts.escapeQuerySpace);for(q in m)if(p.call(m,q)){if(y(m[q])){if(!I(m[q],_[q]))return!1}else if(m[q]!==_[q])return!1;v[q]=!0}for(q in _)if(p.call(_,q)&&!v[q])return!1;return!0},u.preventInvalidHostname=function(r){return this._parts.preventInvalidHostname=!!r,this},u.duplicateQueryParameters=function(r){return this._parts.duplicateQueryParameters=!!r,this},u.escapeQuerySpace=function(r){return this._parts.escapeQuerySpace=!!r,this},o})});function Ot(e){this._ellipsoid=O(e,ze.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(Ot.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}});Ot.prototype.project=function(e,t){let n=this._semimajorAxis,i=e.longitude*n,s=e.latitude*n,o=e.height;return d(t)?(t.x=i,t.y=s,t.z=o,t):new f(i,s,o)};Ot.prototype.unproject=function(e,t){if(!d(e))throw new R("cartesian is required");let n=this._oneOverSemimajorAxis,i=e.x*n,s=e.y*n,o=e.z;return d(t)?(t.longitude=i,t.latitude=s,t.height=o,t):new De(i,s,o)};var an=Ot;var mo={OUTSIDE:-1,INTERSECTING:0,INSIDE:1},Et=Object.freeze(mo);function ho(e,t){this.start=O(e,0),this.stop=O(t,0)}var Kn=ho;function U(e,t){this.center=f.clone(O(e,f.ZERO)),this.radius=O(t,0)}var fn=new f,ln=new f,dn=new f,pn=new f,mn=new f,hn=new f,yn=new f,ce=new f,gn=new f,wn=new f,bn=new f,_n=new f,yo=4/3*J.PI;U.fromPoints=function(e,t){if(d(t)||(t=new U),!d(e)||e.length===0)return t.center=f.clone(f.ZERO,t.center),t.radius=0,t;let n=f.clone(e[0],yn),i=f.clone(n,fn),s=f.clone(n,ln),o=f.clone(n,dn),c=f.clone(n,pn),u=f.clone(n,mn),p=f.clone(n,hn),b=e.length,g;for(g=1;g<b;g++){f.clone(e[g],n);let ie=n.x,X=n.y,W=n.z;ie<i.x&&f.clone(n,i),ie>c.x&&f.clone(n,c),X<s.y&&f.clone(n,s),X>u.y&&f.clone(n,u),W<o.z&&f.clone(n,o),W>p.z&&f.clone(n,p)}let y=f.magnitudeSquared(f.subtract(c,i,ce)),S=f.magnitudeSquared(f.subtract(u,s,ce)),A=f.magnitudeSquared(f.subtract(p,o,ce)),I=i,T=c,k=y;S>k&&(k=S,I=s,T=u),A>k&&(k=A,I=o,T=p);let D=gn;D.x=(I.x+T.x)*.5,D.y=(I.y+T.y)*.5,D.z=(I.z+T.z)*.5;let F=f.magnitudeSquared(f.subtract(T,D,ce)),z=Math.sqrt(F),M=wn;M.x=i.x,M.y=s.y,M.z=o.z;let N=bn;N.x=c.x,N.y=u.y,N.z=p.z;let Y=f.midpoint(M,N,_n),oe=0;for(g=0;g<b;g++){f.clone(e[g],n);let ie=f.magnitude(f.subtract(n,Y,ce));ie>oe&&(oe=ie);let X=f.magnitudeSquared(f.subtract(n,D,ce));if(X>F){let W=Math.sqrt(X);z=(z+W)*.5,F=z*z;let Q=W-z;D.x=(z*D.x+Q*n.x)/W,D.y=(z*D.y+Q*n.y)/W,D.z=(z*D.z+Q*n.z)/W}}return z<oe?(f.clone(D,t.center),t.radius=z):(f.clone(Y,t.center),t.radius=oe),t};var go=new an,wo=new f,bo=new f,cn=new De,un=new De;U.fromRectangle2D=function(e,t,n){return U.fromRectangleWithHeights2D(e,t,0,0,n)};U.fromRectangleWithHeights2D=function(e,t,n,i,s){if(d(s)||(s=new U),!d(e))return s.center=f.clone(f.ZERO,s.center),s.radius=0,s;t=O(t,go),vt.southwest(e,cn),cn.height=n,vt.northeast(e,un),un.height=i;let o=t.project(cn,wo),c=t.project(un,bo),u=c.x-o.x,p=c.y-o.y,b=c.z-o.z;s.radius=Math.sqrt(u*u+p*p+b*b)*.5;let g=s.center;return g.x=o.x+u*.5,g.y=o.y+p*.5,g.z=o.z+b*.5,s};var _o=[];U.fromRectangle3D=function(e,t,n,i){if(t=O(t,ze.WGS84),n=O(n,0),d(i)||(i=new U),!d(e))return i.center=f.clone(f.ZERO,i.center),i.radius=0,i;let s=vt.subsample(e,t,n,_o);return U.fromPoints(s,i)};U.fromVertices=function(e,t,n,i){if(d(i)||(i=new U),!d(e)||e.length===0)return i.center=f.clone(f.ZERO,i.center),i.radius=0,i;t=O(t,f.ZERO),n=O(n,3),w.typeOf.number.greaterThanOrEquals("stride",n,3);let s=yn;s.x=e[0]+t.x,s.y=e[1]+t.y,s.z=e[2]+t.z;let o=f.clone(s,fn),c=f.clone(s,ln),u=f.clone(s,dn),p=f.clone(s,pn),b=f.clone(s,mn),g=f.clone(s,hn),y=e.length,S;for(S=0;S<y;S+=n){let W=e[S]+t.x,Q=e[S+1]+t.y,r=e[S+2]+t.z;s.x=W,s.y=Q,s.z=r,W<o.x&&f.clone(s,o),W>p.x&&f.clone(s,p),Q<c.y&&f.clone(s,c),Q>b.y&&f.clone(s,b),r<u.z&&f.clone(s,u),r>g.z&&f.clone(s,g)}let A=f.magnitudeSquared(f.subtract(p,o,ce)),I=f.magnitudeSquared(f.subtract(b,c,ce)),T=f.magnitudeSquared(f.subtract(g,u,ce)),k=o,D=p,F=A;I>F&&(F=I,k=c,D=b),T>F&&(F=T,k=u,D=g);let z=gn;z.x=(k.x+D.x)*.5,z.y=(k.y+D.y)*.5,z.z=(k.z+D.z)*.5;let M=f.magnitudeSquared(f.subtract(D,z,ce)),N=Math.sqrt(M),Y=wn;Y.x=o.x,Y.y=c.y,Y.z=u.z;let oe=bn;oe.x=p.x,oe.y=b.y,oe.z=g.z;let ie=f.midpoint(Y,oe,_n),X=0;for(S=0;S<y;S+=n){s.x=e[S]+t.x,s.y=e[S+1]+t.y,s.z=e[S+2]+t.z;let W=f.magnitude(f.subtract(s,ie,ce));W>X&&(X=W);let Q=f.magnitudeSquared(f.subtract(s,z,ce));if(Q>M){let r=Math.sqrt(Q);N=(N+r)*.5,M=N*N;let a=r-N;z.x=(N*z.x+a*s.x)/r,z.y=(N*z.y+a*s.y)/r,z.z=(N*z.z+a*s.z)/r}}return N<X?(f.clone(z,i.center),i.radius=N):(f.clone(ie,i.center),i.radius=X),i};U.fromEncodedCartesianVertices=function(e,t,n){if(d(n)||(n=new U),!d(e)||!d(t)||e.length!==t.length||e.length===0)return n.center=f.clone(f.ZERO,n.center),n.radius=0,n;let i=yn;i.x=e[0]+t[0],i.y=e[1]+t[1],i.z=e[2]+t[2];let s=f.clone(i,fn),o=f.clone(i,ln),c=f.clone(i,dn),u=f.clone(i,pn),p=f.clone(i,mn),b=f.clone(i,hn),g=e.length,y;for(y=0;y<g;y+=3){let X=e[y]+t[y],W=e[y+1]+t[y+1],Q=e[y+2]+t[y+2];i.x=X,i.y=W,i.z=Q,X<s.x&&f.clone(i,s),X>u.x&&f.clone(i,u),W<o.y&&f.clone(i,o),W>p.y&&f.clone(i,p),Q<c.z&&f.clone(i,c),Q>b.z&&f.clone(i,b)}let S=f.magnitudeSquared(f.subtract(u,s,ce)),A=f.magnitudeSquared(f.subtract(p,o,ce)),I=f.magnitudeSquared(f.subtract(b,c,ce)),T=s,k=u,D=S;A>D&&(D=A,T=o,k=p),I>D&&(D=I,T=c,k=b);let F=gn;F.x=(T.x+k.x)*.5,F.y=(T.y+k.y)*.5,F.z=(T.z+k.z)*.5;let z=f.magnitudeSquared(f.subtract(k,F,ce)),M=Math.sqrt(z),N=wn;N.x=s.x,N.y=o.y,N.z=c.z;let Y=bn;Y.x=u.x,Y.y=p.y,Y.z=b.z;let oe=f.midpoint(N,Y,_n),ie=0;for(y=0;y<g;y+=3){i.x=e[y]+t[y],i.y=e[y+1]+t[y+1],i.z=e[y+2]+t[y+2];let X=f.magnitude(f.subtract(i,oe,ce));X>ie&&(ie=X);let W=f.magnitudeSquared(f.subtract(i,F,ce));if(W>z){let Q=Math.sqrt(W);M=(M+Q)*.5,z=M*M;let r=Q-M;F.x=(M*F.x+r*i.x)/Q,F.y=(M*F.y+r*i.y)/Q,F.z=(M*F.z+r*i.z)/Q}}return M<ie?(f.clone(F,n.center),n.radius=M):(f.clone(oe,n.center),n.radius=ie),n};U.fromCornerPoints=function(e,t,n){w.typeOf.object("corner",e),w.typeOf.object("oppositeCorner",t),d(n)||(n=new U);let i=f.midpoint(e,t,n.center);return n.radius=f.distance(i,t),n};U.fromEllipsoid=function(e,t){return w.typeOf.object("ellipsoid",e),d(t)||(t=new U),f.clone(f.ZERO,t.center),t.radius=e.maximumRadius,t};var So=new f;U.fromBoundingSpheres=function(e,t){if(d(t)||(t=new U),!d(e)||e.length===0)return t.center=f.clone(f.ZERO,t.center),t.radius=0,t;let n=e.length;if(n===1)return U.clone(e[0],t);if(n===2)return U.union(e[0],e[1],t);let i=[],s;for(s=0;s<n;s++)i.push(e[s].center);t=U.fromPoints(i,t);let o=t.center,c=t.radius;for(s=0;s<n;s++){let u=e[s];c=Math.max(c,f.distance(o,u.center,So)+u.radius)}return t.radius=c,t};var xo=new f,vo=new f,Oo=new f;U.fromOrientedBoundingBox=function(e,t){w.defined("orientedBoundingBox",e),d(t)||(t=new U);let n=e.halfAxes,i=B.getColumn(n,0,xo),s=B.getColumn(n,1,vo),o=B.getColumn(n,2,Oo);return f.add(i,s,i),f.add(i,o,i),t.center=f.clone(e.center,t.center),t.radius=f.magnitude(i),t};var Eo=new f,Co=new f;U.fromTransformation=function(e,t){w.typeOf.object("transformation",e),d(t)||(t=new U);let n=L.getTranslation(e,Eo),i=L.getScale(e,Co),s=.5*f.magnitude(i);return t.center=f.clone(n,t.center),t.radius=s,t};U.clone=function(e,t){if(d(e))return d(t)?(t.center=f.clone(e.center,t.center),t.radius=e.radius,t):new U(e.center,e.radius)};U.packedLength=4;U.pack=function(e,t,n){w.typeOf.object("value",e),w.defined("array",t),n=O(n,0);let i=e.center;return t[n++]=i.x,t[n++]=i.y,t[n++]=i.z,t[n]=e.radius,t};U.unpack=function(e,t,n){w.defined("array",e),t=O(t,0),d(n)||(n=new U);let i=n.center;return i.x=e[t++],i.y=e[t++],i.z=e[t++],n.radius=e[t],n};var Ao=new f,Ro=new f;U.union=function(e,t,n){w.typeOf.object("left",e),w.typeOf.object("right",t),d(n)||(n=new U);let i=e.center,s=e.radius,o=t.center,c=t.radius,u=f.subtract(o,i,Ao),p=f.magnitude(u);if(s>=p+c)return e.clone(n),n;if(c>=p+s)return t.clone(n),n;let b=(s+p+c)*.5,g=f.multiplyByScalar(u,(-s+b)/p,Ro);return f.add(g,i,g),f.clone(g,n.center),n.radius=b,n};var Po=new f;U.expand=function(e,t,n){w.typeOf.object("sphere",e),w.typeOf.object("point",t),n=U.clone(e,n);let i=f.magnitude(f.subtract(t,n.center,Po));return i>n.radius&&(n.radius=i),n};U.intersectPlane=function(e,t){w.typeOf.object("sphere",e),w.typeOf.object("plane",t);let n=e.center,i=e.radius,s=t.normal,o=f.dot(s,n)+t.distance;return o<-i?Et.OUTSIDE:o<i?Et.INTERSECTING:Et.INSIDE};U.transform=function(e,t,n){return w.typeOf.object("sphere",e),w.typeOf.object("transform",t),d(n)||(n=new U),n.center=L.multiplyByPoint(t,e.center,n.center),n.radius=L.getMaximumScale(t)*e.radius,n};var To=new f;U.distanceSquaredTo=function(e,t){w.typeOf.object("sphere",e),w.typeOf.object("cartesian",t);let n=f.subtract(e.center,t,To),i=f.magnitude(n)-e.radius;return i<=0?0:i*i};U.transformWithoutScale=function(e,t,n){return w.typeOf.object("sphere",e),w.typeOf.object("transform",t),d(n)||(n=new U),n.center=L.multiplyByPoint(t,e.center,n.center),n.radius=e.radius,n};var Io=new f;U.computePlaneDistances=function(e,t,n,i){w.typeOf.object("sphere",e),w.typeOf.object("position",t),w.typeOf.object("direction",n),d(i)||(i=new Kn);let s=f.subtract(e.center,t,Io),o=f.dot(n,s);return i.start=o-e.radius,i.stop=o+e.radius,i};var er=new f,Uo=new f,Do=new f,zo=new f,qo=new f,ko=new De,tr=new Array(8);for(let e=0;e<8;++e)tr[e]=new f;var Fo=new an;U.projectTo2D=function(e,t,n){w.typeOf.object("sphere",e),t=O(t,Fo);let i=t.ellipsoid,s=e.center,o=e.radius,c;f.equals(s,f.ZERO)?c=f.clone(f.UNIT_X,er):c=i.geodeticSurfaceNormal(s,er);let u=f.cross(f.UNIT_Z,c,Uo);f.normalize(u,u);let p=f.cross(c,u,Do);f.normalize(p,p),f.multiplyByScalar(c,o,c),f.multiplyByScalar(p,o,p),f.multiplyByScalar(u,o,u);let b=f.negate(p,qo),g=f.negate(u,zo),y=tr,S=y[0];f.add(c,p,S),f.add(S,u,S),S=y[1],f.add(c,p,S),f.add(S,g,S),S=y[2],f.add(c,b,S),f.add(S,g,S),S=y[3],f.add(c,b,S),f.add(S,u,S),f.negate(c,c),S=y[4],f.add(c,p,S),f.add(S,u,S),S=y[5],f.add(c,p,S),f.add(S,g,S),S=y[6],f.add(c,b,S),f.add(S,g,S),S=y[7],f.add(c,b,S),f.add(S,u,S);let A=y.length;for(let D=0;D<A;++D){let F=y[D];f.add(s,F,F);let z=i.cartesianToCartographic(F,ko);t.project(z,F)}n=U.fromPoints(y,n),s=n.center;let I=s.x,T=s.y,k=s.z;return s.x=k,s.y=I,s.z=T,n};U.isOccluded=function(e,t){return w.typeOf.object("sphere",e),w.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)};U.equals=function(e,t){return e===t||d(e)&&d(t)&&f.equals(e.center,t.center)&&e.radius===t.radius};U.prototype.intersectPlane=function(e){return U.intersectPlane(this,e)};U.prototype.distanceSquaredTo=function(e){return U.distanceSquaredTo(this,e)};U.prototype.computePlaneDistances=function(e,t,n){return U.computePlaneDistances(this,e,t,n)};U.prototype.isOccluded=function(e){return U.isOccluded(this,e)};U.prototype.equals=function(e){return U.equals(this,e)};U.prototype.clone=function(e){return U.clone(this,e)};U.prototype.volume=function(){let e=this.radius;return yo*e*e*e};var Qs=U;function Mo(e,t,n){w.defined("array",e),w.defined("itemToFind",t),w.defined("comparator",n);let i=0,s=e.length-1,o,c;for(;i<=s;){if(o=~~((i+s)/2),c=n(e[o],t),c<0){i=o+1;continue}if(c>0){s=o-1;continue}return o}return~(s+1)}var Qe=Mo;function jo(e,t,n,i,s){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=i,this.ut1MinusUtc=s}var ut=jo;function No(e,t,n,i,s,o,c,u){this.year=e,this.month=t,this.day=n,this.hour=i,this.minute=s,this.second=o,this.millisecond=c,this.isLeapSecond=u}var Ct=No;function Bo(e){if(e===null||isNaN(e))throw new R("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var Sn=Bo;function Lo(e,t){this.julianDate=e,this.offset=t}var Z=Lo;var Qo={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},K=Object.freeze(Qo);var $o={UTC:0,TAI:1},j=Object.freeze($o);var rr=new Ct,At=[31,28,31,30,31,30,31,31,30,31,30,31],Rt=29;function xn(e,t){return C.compare(e.julianDate,t.julianDate)}var Ke=new Z;function Tt(e){Ke.julianDate=e;let t=C.leapSeconds,n=Qe(t,Ke,xn);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let i=t[n].offset;n>0&&C.secondsDifference(t[n].julianDate,e)>i&&(n--,i=t[n].offset),C.addSeconds(e,i,e)}function nr(e,t){Ke.julianDate=e;let n=C.leapSeconds,i=Qe(n,Ke,xn);if(i<0&&(i=~i),i===0)return C.addSeconds(e,-n[0].offset,t);if(i>=n.length)return C.addSeconds(e,-n[i-1].offset,t);let s=C.secondsDifference(n[i].julianDate,e);if(s===0)return C.addSeconds(e,-n[i].offset,t);if(!(s<=1))return C.addSeconds(e,-n[--i].offset,t)}function ke(e,t,n){let i=t/K.SECONDS_PER_DAY|0;return e+=i,t-=K.SECONDS_PER_DAY*i,t<0&&(e--,t+=K.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function vn(e,t,n,i,s,o,c){let u=(t-14)/12|0,p=e+4800+u,b=(1461*p/4|0)+(367*(t-2-12*u)/12|0)-(3*((p+100)/100|0)/4|0)+n-32075;i=i-12,i<0&&(i+=24);let g=o+(i*K.SECONDS_PER_HOUR+s*K.SECONDS_PER_MINUTE+c*K.SECONDS_PER_MILLISECOND);return g>=43200&&(b-=1),[b,g]}var Wo=/^(\d{4})$/,Vo=/^(\d{4})-(\d{2})$/,Ho=/^(\d{4})-?(\d{3})$/,Yo=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,Zo=/^(\d{4})-?(\d{2})-?(\d{2})$/,On=/([Z+\-])?(\d{2})?:?(\d{2})?$/,Jo=/^(\d{2})(\.\d+)?/.source+On.source,Xo=/^(\d{2}):?(\d{2})(\.\d+)?/.source+On.source,Go=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+On.source,ve="Invalid ISO 8601 date.";function C(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=O(e,0),t=O(t,0),n=O(n,j.UTC);let i=e|0;t=t+(e-i)*K.SECONDS_PER_DAY,ke(i,t,this),n===j.UTC&&Tt(this)}C.fromGregorianDate=function(e,t){if(!(e instanceof Ct))throw new R("date must be a valid GregorianDate.");let n=vn(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return d(t)?(ke(n[0],n[1],t),Tt(t),t):new C(n[0],n[1],j.UTC)};C.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new R("date must be a valid JavaScript Date.");let n=vn(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return d(t)?(ke(n[0],n[1],t),Tt(t),t):new C(n[0],n[1],j.UTC)};C.fromIso8601=function(e,t){if(typeof e!="string")throw new R(ve);e=e.replace(",",".");let n=e.split("T"),i,s=1,o=1,c=0,u=0,p=0,b=0,g=n[0],y=n[1],S,A;if(!d(g))throw new R(ve);let I;if(n=g.match(Zo),n!==null){if(I=g.split("-").length-1,I>0&&I!==2)throw new R(ve);i=+n[1],s=+n[2],o=+n[3]}else if(n=g.match(Vo),n!==null)i=+n[1],s=+n[2];else if(n=g.match(Wo),n!==null)i=+n[1];else{let F;if(n=g.match(Ho),n!==null){if(i=+n[1],F=+n[2],A=Sn(i),F<1||A&&F>366||!A&&F>365)throw new R(ve)}else if(n=g.match(Yo),n!==null){i=+n[1];let z=+n[2],M=+n[3]||0;if(I=g.split("-").length-1,I>0&&(!d(n[3])&&I!==1||d(n[3])&&I!==2))throw new R(ve);let N=new Date(Date.UTC(i,0,4));F=z*7+M-N.getUTCDay()-3}else throw new R(ve);S=new Date(Date.UTC(i,0,1)),S.setUTCDate(F),s=S.getUTCMonth()+1,o=S.getUTCDate()}if(A=Sn(i),s<1||s>12||o<1||(s!==2||!A)&&o>At[s-1]||A&&s===2&&o>Rt)throw new R(ve);let T;if(d(y)){if(n=y.match(Go),n!==null){if(I=y.split(":").length-1,I>0&&I!==2&&I!==3)throw new R(ve);c=+n[1],u=+n[2],p=+n[3],b=+(n[4]||0)*1e3,T=5}else if(n=y.match(Xo),n!==null){if(I=y.split(":").length-1,I>2)throw new R(ve);c=+n[1],u=+n[2],p=+(n[3]||0)*60,T=4}else if(n=y.match(Jo),n!==null)c=+n[1],u=+(n[2]||0)*60,T=3;else throw new R(ve);if(u>=60||p>=61||c>24||c===24&&(u>0||p>0||b>0))throw new R(ve);let F=n[T],z=+n[T+1],M=+(n[T+2]||0);switch(F){case"+":c=c-z,u=u-M;break;case"-":c=c+z,u=u+M;break;case"Z":break;default:u=u+new Date(Date.UTC(i,s-1,o,c,u)).getTimezoneOffset();break}}let k=p===60;for(k&&p--;u>=60;)u-=60,c++;for(;c>=24;)c-=24,o++;for(S=A&&s===2?Rt:At[s-1];o>S;)o-=S,s++,s>12&&(s-=12,i++),S=A&&s===2?Rt:At[s-1];for(;u<0;)u+=60,c--;for(;c<0;)c+=24,o--;for(;o<1;)s--,s<1&&(s+=12,i--),S=A&&s===2?Rt:At[s-1],o+=S;let D=vn(i,s,o,c,u,p,b);return d(t)?(ke(D[0],D[1],t),Tt(t)):t=new C(D[0],D[1],j.UTC),k&&C.addSeconds(t,1,t),t};C.now=function(e){return C.fromDate(new Date,e)};var Pt=new C(0,0,j.TAI);C.toGregorianDate=function(e,t){if(!d(e))throw new R("julianDate is required.");let n=!1,i=nr(e,Pt);d(i)||(C.addSeconds(e,-1,Pt),i=nr(Pt,Pt),n=!0);let s=i.dayNumber,o=i.secondsOfDay;o>=43200&&(s+=1);let c=s+68569|0,u=4*c/146097|0;c=c-((146097*u+3)/4|0)|0;let p=4e3*(c+1)/1461001|0;c=c-(1461*p/4|0)+31|0;let b=80*c/2447|0,g=c-(2447*b/80|0)|0;c=b/11|0;let y=b+2-12*c|0,S=100*(u-49)+p+c|0,A=o/K.SECONDS_PER_HOUR|0,I=o-A*K.SECONDS_PER_HOUR,T=I/K.SECONDS_PER_MINUTE|0;I=I-T*K.SECONDS_PER_MINUTE;let k=I|0,D=(I-k)/K.SECONDS_PER_MILLISECOND;return A+=12,A>23&&(A-=24),n&&(k+=1),d(t)?(t.year=S,t.month=y,t.day=g,t.hour=A,t.minute=T,t.second=k,t.millisecond=D,t.isLeapSecond=n,t):new Ct(S,y,g,A,T,k,D,n)};C.toDate=function(e){if(!d(e))throw new R("julianDate is required.");let t=C.toGregorianDate(e,rr),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))};C.toIso8601=function(e,t){if(!d(e))throw new R("julianDate is required.");let n=C.toGregorianDate(e,rr),i=n.year,s=n.month,o=n.day,c=n.hour,u=n.minute,p=n.second,b=n.millisecond;i===1e4&&s===1&&o===1&&c===0&&u===0&&p===0&&b===0&&(i=9999,s=12,o=31,c=24);let g;return!d(t)&&b!==0?(g=(b*.01).toString().replace(".",""),`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}.${g}Z`):!d(t)||t===0?`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}Z`:(g=(b*.01).toFixed(t).replace(".","").slice(0,t),`${i.toString().padStart(4,"0")}-${s.toString().padStart(2,"0")}-${o.toString().padStart(2,"0")}T${c.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${p.toString().padStart(2,"0")}.${g}Z`)};C.clone=function(e,t){if(d(e))return d(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new C(e.dayNumber,e.secondsOfDay,j.TAI)};C.compare=function(e,t){if(!d(e))throw new R("left is required.");if(!d(t))throw new R("right is required.");let n=e.dayNumber-t.dayNumber;return n!==0?n:e.secondsOfDay-t.secondsOfDay};C.equals=function(e,t){return e===t||d(e)&&d(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay};C.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||d(e)&&d(t)&&Math.abs(C.secondsDifference(e,t))<=n};C.totalDays=function(e){if(!d(e))throw new R("julianDate is required.");return e.dayNumber+e.secondsOfDay/K.SECONDS_PER_DAY};C.secondsDifference=function(e,t){if(!d(e))throw new R("left is required.");if(!d(t))throw new R("right is required.");return(e.dayNumber-t.dayNumber)*K.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)};C.daysDifference=function(e,t){if(!d(e))throw new R("left is required.");if(!d(t))throw new R("right is required.");let n=e.dayNumber-t.dayNumber,i=(e.secondsOfDay-t.secondsOfDay)/K.SECONDS_PER_DAY;return n+i};C.computeTaiMinusUtc=function(e){Ke.julianDate=e;let t=C.leapSeconds,n=Qe(t,Ke,xn);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset};C.addSeconds=function(e,t,n){if(!d(e))throw new R("julianDate is required.");if(!d(t))throw new R("seconds is required.");if(!d(n))throw new R("result is required.");return ke(e.dayNumber,e.secondsOfDay+t,n)};C.addMinutes=function(e,t,n){if(!d(e))throw new R("julianDate is required.");if(!d(t))throw new R("minutes is required.");if(!d(n))throw new R("result is required.");let i=e.secondsOfDay+t*K.SECONDS_PER_MINUTE;return ke(e.dayNumber,i,n)};C.addHours=function(e,t,n){if(!d(e))throw new R("julianDate is required.");if(!d(t))throw new R("hours is required.");if(!d(n))throw new R("result is required.");let i=e.secondsOfDay+t*K.SECONDS_PER_HOUR;return ke(e.dayNumber,i,n)};C.addDays=function(e,t,n){if(!d(e))throw new R("julianDate is required.");if(!d(t))throw new R("days is required.");if(!d(n))throw new R("result is required.");let i=e.dayNumber+t;return ke(i,e.secondsOfDay,n)};C.lessThan=function(e,t){return C.compare(e,t)<0};C.lessThanOrEquals=function(e,t){return C.compare(e,t)<=0};C.greaterThan=function(e,t){return C.compare(e,t)>0};C.greaterThanOrEquals=function(e,t){return C.compare(e,t)>=0};C.prototype.clone=function(e){return C.clone(this,e)};C.prototype.equals=function(e){return C.equals(this,e)};C.prototype.equalsEpsilon=function(e,t){return C.equalsEpsilon(this,e,t)};C.prototype.toString=function(){return C.toIso8601(this)};C.leapSeconds=[new Z(new C(2441317,43210,j.TAI),10),new Z(new C(2441499,43211,j.TAI),11),new Z(new C(2441683,43212,j.TAI),12),new Z(new C(2442048,43213,j.TAI),13),new Z(new C(2442413,43214,j.TAI),14),new Z(new C(2442778,43215,j.TAI),15),new Z(new C(2443144,43216,j.TAI),16),new Z(new C(2443509,43217,j.TAI),17),new Z(new C(2443874,43218,j.TAI),18),new Z(new C(2444239,43219,j.TAI),19),new Z(new C(2444786,43220,j.TAI),20),new Z(new C(2445151,43221,j.TAI),21),new Z(new C(2445516,43222,j.TAI),22),new Z(new C(2446247,43223,j.TAI),23),new Z(new C(2447161,43224,j.TAI),24),new Z(new C(2447892,43225,j.TAI),25),new Z(new C(2448257,43226,j.TAI),26),new Z(new C(2448804,43227,j.TAI),27),new Z(new C(2449169,43228,j.TAI),28),new Z(new C(2449534,43229,j.TAI),29),new Z(new C(2450083,43230,j.TAI),30),new Z(new C(2450630,43231,j.TAI),31),new Z(new C(2451179,43232,j.TAI),32),new Z(new C(2453736,43233,j.TAI),33),new Z(new C(2454832,43234,j.TAI),34),new Z(new C(2456109,43235,j.TAI),35),new Z(new C(2457204,43236,j.TAI),36),new Z(new C(2457754,43237,j.TAI),37)];var ue=C;var zr=Ge($e(),1);function Ko(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var fr=Ko;function lr(e,t){if(e===null||typeof e!="object")return e;t=O(t,!1);let n=new e.constructor;for(let i in e)if(e.hasOwnProperty(i)){let s=e[i];t&&(s=lr(s,t)),n[i]=s}return n}var nt=lr;function ei(){let e,t,n=new Promise(function(i,s){e=i,t=s});return{resolve:e,reject:t,promise:n}}var We=ei;var dr=Ge($e(),1);function En(e,t){let n;return typeof document<"u"&&(n=document),En._implementation(e,t,n)}En._implementation=function(e,t,n){if(!d(e))throw new R("relative uri is required.");if(!d(t)){if(typeof n>"u")return e;t=O(n.baseURI,n.location.href)}let i=new dr.default(e);return i.scheme()!==""?i.toString():i.absoluteTo(t).toString()};var ft=En;var pr=Ge($e(),1);function ti(e,t){if(!d(e))throw new R("uri is required.");let n="",i=e.lastIndexOf("/");return i!==-1&&(n=e.substring(0,i+1)),t&&(e=new pr.default(e),e.query().length!==0&&(n+=`?${e.query()}`),e.fragment().length!==0&&(n+=`#${e.fragment()}`)),n}var mr=ti;var hr=Ge($e(),1);function ni(e){if(!d(e))throw new R("uri is required.");let t=new hr.default(e);t.normalize();let n=t.path(),i=n.lastIndexOf("/");return i!==-1&&(n=n.substr(i+1)),i=n.lastIndexOf("."),i===-1?n="":n=n.substr(i+1),n}var yr=ni;var gr={};function ri(e,t,n){d(t)||(t=e.width),d(n)||(n=e.height);let i=gr[t];d(i)||(i={},gr[t]=i);let s=i[n];if(!d(s)){let o=document.createElement("canvas");o.width=t,o.height=n,s=o.getContext("2d",{willReadFrequently:!0}),s.globalCompositeOperation="copy",i[n]=s}return s.drawImage(e,0,0,t,n),s.getImageData(0,0,t,n).data}var Cn=ri;var oi=/^blob:/i;function ii(e){return w.typeOf.string("uri",e),oi.test(e)}var zt=ii;var Ce;function si(e){d(Ce)||(Ce=document.createElement("a")),Ce.href=window.location.href;let t=Ce.host,n=Ce.protocol;return Ce.href=e,Ce.href=Ce.href,n!==Ce.protocol||t!==Ce.host}var wr=si;var ai=/^data:/i;function ci(e){return w.typeOf.string("uri",e),ai.test(e)}var qt=ci;function ui(e){let t=document.createElement("script");return t.async=!0,t.src=e,new Promise((n,i)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");let s=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,s.removeChild(t),n()},t.onerror=function(o){i(o)},s.appendChild(t)})}var br=ui;function fi(e){if(!d(e))throw new R("obj is required.");let t="";for(let n in e)if(e.hasOwnProperty(n)){let i=e[n],s=`${encodeURIComponent(n)}=`;if(Array.isArray(i))for(let o=0,c=i.length;o<c;++o)t+=`${s+encodeURIComponent(i[o])}&`;else t+=`${s+encodeURIComponent(i)}&`}return t=t.slice(0,-1),t}var _r=fi;function li(e){if(!d(e))throw new R("queryString is required.");let t={};if(e==="")return t;let n=e.replace(/\+/g,"%20").split(/[&;]/);for(let i=0,s=n.length;i<s;++i){let o=n[i].split("="),c=decodeURIComponent(o[0]),u=o[1];d(u)?u=decodeURIComponent(u):u="";let p=t[c];typeof p=="string"?t[c]=[p,u]:Array.isArray(p)?p.push(u):t[c]=u}return t}var Sr=li;var di={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},te=Object.freeze(di);var pi={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},xr=Object.freeze(pi);function kt(e){e=O(e,O.EMPTY_OBJECT);let t=O(e.throttleByServer,!1),n=O(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=O(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=O(e.type,xr.OTHER),this.serverKey=e.serverKey,this.state=te.UNISSUED,this.deferred=void 0,this.cancelled=!1}kt.prototype.cancel=function(){this.cancelled=!0};kt.prototype.clone=function(e){return d(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=te.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new kt(this)};var vr=kt;function mi(e){let t={};if(!e)return t;let n=e.split(`\r
`);for(let i=0;i<n.length;++i){let s=n[i],o=s.indexOf(": ");if(o>0){let c=s.substring(0,o),u=s.substring(o+2);t[c]=u}}return t}var Or=mi;function Er(e,t,n){this.statusCode=e,this.response=t,this.responseHeaders=n,typeof this.responseHeaders=="string"&&(this.responseHeaders=Or(this.responseHeaders))}Er.prototype.toString=function(){let e="Request has failed.";return d(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var lt=Er;var Ft=Ge($e(),1);function dt(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(dt.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});dt.prototype.addEventListener=function(e,t){w.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(t);let n=this;return function(){n.removeEventListener(e,t)}};dt.prototype.removeEventListener=function(e,t){w.typeOf.func("listener",e);let n=this._listeners,i=this._scopes,s=-1;for(let o=0;o<n.length;o++)if(n[o]===e&&i[o]===t){s=o;break}return s!==-1?(this._insideRaiseEvent?(this._toRemove.push(s),n[s]=void 0,i[s]=void 0):(n.splice(s,1),i.splice(s,1)),!0):!1};function hi(e,t){return t-e}dt.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,t=this._listeners,n=this._scopes,i=t.length;for(e=0;e<i;e++){let o=t[e];d(o)&&t[e].apply(n[e],arguments)}let s=this._toRemove;if(i=s.length,i>0){for(s.sort(hi),e=0;e<i;e++){let o=s[e];t.splice(o,1),n.splice(o,1)}s.length=0}this._insideRaiseEvent=!1};var Cr=dt;function Ve(e){w.typeOf.object("options",e),w.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(Ve.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){w.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let t=this._length;if(e<t){let n=this._array;for(let i=e;i<t;++i)n[i]=void 0;this._length=e,n.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function An(e,t,n){let i=e[t];e[t]=e[n],e[n]=i}Ve.prototype.reserve=function(e){e=O(e,this._length),this._array.length=e};Ve.prototype.heapify=function(e){e=O(e,0);let t=this._length,n=this._comparator,i=this._array,s=-1,o=!0;for(;o;){let c=2*(e+1),u=c-1;u<t&&n(i[u],i[e])<0?s=u:s=e,c<t&&n(i[c],i[s])<0&&(s=c),s!==e?(An(i,s,e),e=s):o=!1}};Ve.prototype.resort=function(){let e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)};Ve.prototype.insert=function(e){w.defined("element",e);let t=this._array,n=this._comparator,i=this._maximumLength,s=this._length++;for(s<t.length?t[s]=e:t.push(e);s!==0;){let c=Math.floor((s-1)/2);if(n(t[s],t[c])<0)An(t,s,c),s=c;else break}let o;return d(i)&&this._length>i&&(o=t[i],this._length=i),o};Ve.prototype.pop=function(e){if(e=O(e,0),this._length===0)return;w.typeOf.number.lessThan("index",e,this._length);let t=this._array,n=t[e];return An(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};var Ar=Ve;function yi(e,t){return e.priority-t.priority}var H={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},rt=20,le=new Ar({comparator:yi});le.maximumLength=rt;le.reserve(rt);var Ae=[],Pe={},gi=typeof document<"u"?new Ft.default(document.location.href):new Ft.default,Mt=new Cr;function ee(){}ee.maximumRequests=50;ee.maximumRequestsPerServer=18;ee.requestsByServer={};ee.throttleRequests=!0;ee.debugShowStatistics=!1;ee.requestCompletedEvent=Mt;Object.defineProperties(ee,{statistics:{get:function(){return H}},priorityHeapLength:{get:function(){return rt},set:function(e){if(e<rt)for(;le.length>e;){let t=le.pop();He(t)}rt=e,le.maximumLength=e,le.reserve(e)}}});function Rr(e){d(e.priorityFunction)&&(e.priority=e.priorityFunction())}ee.serverHasOpenSlots=function(e,t){t=O(t,1);let n=O(ee.requestsByServer[e],ee.maximumRequestsPerServer);return Pe[e]+t<=n};ee.heapHasOpenSlots=function(e){return le.length+e<=rt};function Pr(e){return e.state===te.UNISSUED&&(e.state=te.ISSUED,e.deferred=We()),e.deferred.promise}function wi(e){return function(t){if(e.state===te.CANCELLED)return;let n=e.deferred;--H.numberOfActiveRequests,--Pe[e.serverKey],Mt.raiseEvent(),e.state=te.RECEIVED,e.deferred=void 0,n.resolve(t)}}function bi(e){return function(t){e.state!==te.CANCELLED&&(++H.numberOfFailedRequests,--H.numberOfActiveRequests,--Pe[e.serverKey],Mt.raiseEvent(t),e.state=te.FAILED,e.deferred.reject(t))}}function Tr(e){let t=Pr(e);return e.state=te.ACTIVE,Ae.push(e),++H.numberOfActiveRequests,++H.numberOfActiveRequestsEver,++Pe[e.serverKey],e.requestFunction().then(wi(e)).catch(bi(e)),t}function He(e){let t=e.state===te.ACTIVE;if(e.state=te.CANCELLED,++H.numberOfCancelledRequests,d(e.deferred)){let n=e.deferred;e.deferred=void 0,n.reject()}t&&(--H.numberOfActiveRequests,--Pe[e.serverKey],++H.numberOfCancelledActiveRequests),d(e.cancelFunction)&&e.cancelFunction()}ee.update=function(){let e,t,n=0,i=Ae.length;for(e=0;e<i;++e){if(t=Ae[e],t.cancelled&&He(t),t.state!==te.ACTIVE){++n;continue}n>0&&(Ae[e-n]=t)}Ae.length-=n;let s=le.internalArray,o=le.length;for(e=0;e<o;++e)Rr(s[e]);le.resort();let c=Math.max(ee.maximumRequests-Ae.length,0),u=0;for(;u<c&&le.length>0;){if(t=le.pop(),t.cancelled){He(t);continue}if(t.throttleByServer&&!ee.serverHasOpenSlots(t.serverKey)){He(t);continue}Tr(t),++u}_i()};ee.getServerKey=function(e){w.typeOf.string("url",e);let t=new Ft.default(e);t.scheme()===""&&(t=t.absoluteTo(gi),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${t.scheme()==="https"?"443":"80"}`);let i=Pe[n];return d(i)||(Pe[n]=0),n};ee.request=function(e){if(w.typeOf.object("request",e),w.typeOf.string("request.url",e.url),w.typeOf.func("request.requestFunction",e.requestFunction),qt(e.url)||zt(e.url))return Mt.raiseEvent(),e.state=te.RECEIVED,e.requestFunction();if(++H.numberOfAttemptedRequests,d(e.serverKey)||(e.serverKey=ee.getServerKey(e.url)),ee.throttleRequests&&e.throttleByServer&&!ee.serverHasOpenSlots(e.serverKey))return;if(!ee.throttleRequests||!e.throttle)return Tr(e);if(Ae.length>=ee.maximumRequests)return;Rr(e);let t=le.insert(e);if(d(t)){if(t===e)return;He(t)}return Pr(e)};function _i(){ee.debugShowStatistics&&(H.numberOfActiveRequests===0&&H.lastNumberOfActiveRequests>0&&(H.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${H.numberOfAttemptedRequests}`),H.numberOfAttemptedRequests=0),H.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${H.numberOfCancelledRequests}`),H.numberOfCancelledRequests=0),H.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${H.numberOfCancelledActiveRequests}`),H.numberOfCancelledActiveRequests=0),H.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${H.numberOfFailedRequests}`),H.numberOfFailedRequests=0)),H.lastNumberOfActiveRequests=H.numberOfActiveRequests)}ee.clearForSpecs=function(){for(;le.length>0;){let t=le.pop();He(t)}let e=Ae.length;for(let t=0;t<e;++t)He(Ae[t]);Ae.length=0,Pe={},H.numberOfAttemptedRequests=0,H.numberOfActiveRequests=0,H.numberOfCancelledRequests=0,H.numberOfCancelledActiveRequests=0,H.numberOfFailedRequests=0,H.numberOfActiveRequestsEver=0,H.lastNumberOfActiveRequests=0};ee.numberOfActiveRequestsByServer=function(e){return Pe[e]};ee.requestHeap=le;var jt=ee;var Ir=Ge($e(),1);var pt={},ot={};pt.add=function(e,t){if(!d(e))throw new R("host is required.");if(!d(t)||t<=0)throw new R("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;d(ot[n])||(ot[n]=!0)};pt.remove=function(e,t){if(!d(e))throw new R("host is required.");if(!d(t)||t<=0)throw new R("port is required to be greater than 0.");let n=`${e.toLowerCase()}:${t}`;d(ot[n])&&delete ot[n]};function Si(e){let t=new Ir.default(e);t.normalize();let n=t.authority();if(n.length!==0){if(t.authority(n),n.indexOf("@")!==-1&&(n=n.split("@")[1]),n.indexOf(":")===-1){let i=t.scheme();if(i.length===0&&(i=window.location.protocol,i=i.substring(0,i.length-1)),i==="http")n+=":80";else if(i==="https")n+=":443";else return}return n}}pt.contains=function(e){if(!d(e))throw new R("url is required.");let t=Si(e);return!!(d(t)&&d(ot[t]))};pt.clear=function(){ot={}};var Rn=pt;var qr=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function E(e){e=O(e,O.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),w.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Re(e.templateValues,{}),this._queryParameters=Re(e.queryParameters,{}),this.headers=Re(e.headers,{}),this.request=O(e.request,new vr),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=O(e.retryAttempts,0),this._retryCount=0,O(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Re(e,t){return d(e)?nt(e):t}E.createIfNeeded=function(e){return e instanceof E?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new E({url:e})};var it;E.supportsImageBitmapOptions=function(){return d(it)?it:typeof createImageBitmap!="function"?(it=Promise.resolve(!1),it):(it=E.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(t){let n={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(t,n),createImageBitmap(t)])}).then(function(t){let n=Cn(t[0]),i=Cn(t[1]);return n[1]!==i[1]}).catch(function(){return!1}),it)};Object.defineProperties(E,{isBlobSupported:{get:function(){return qr}}});Object.defineProperties(E.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return yr(this._url)}},isDataUri:{get:function(){return qt(this._url)}},isBlobUri:{get:function(){return zt(this._url)}},isCrossOriginUrl:{get:function(){return wr(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});E.prototype.toString=function(){return this.getUrlComponent(!0,!0)};E.prototype.parseUrl=function(e,t,n,i){let s=new zr.default(e),o=xi(s.query());this._queryParameters=t?Bt(o,this.queryParameters,n):o,s.search(""),s.fragment(""),d(i)&&s.scheme()===""&&(s=s.absoluteTo(ft(i))),this._url=s.toString()};function xi(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Sr(e)}function Bt(e,t,n){if(!n)return qe(e,t);let i=nt(e,!0);for(let s in t)if(t.hasOwnProperty(s)){let o=i[s],c=t[s];d(o)?(Array.isArray(o)||(o=i[s]=[o]),i[s]=o.concat(c)):i[s]=Array.isArray(c)?c.slice():c}return i}E.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;let n=this._url;e&&(n=`${n}${vi(this.queryParameters)}`),n=n.replace(/%7B/g,"{").replace(/%7D/g,"}");let i=this._templateValues;return Object.keys(i).length>0&&(n=n.replace(/{(.*?)}/g,function(s,o){let c=i[o];return d(c)?encodeURIComponent(c):s})),t&&d(this.proxy)&&(n=this.proxy.getURL(n)),n};function vi(e){let t=Object.keys(e);return t.length===0?"":t.length===1&&!d(e[t[0]])?`?${t[0]}`:`?${_r(e)}`}E.prototype.setQueryParameters=function(e,t){t?this._queryParameters=Bt(this._queryParameters,e,!1):this._queryParameters=Bt(e,this._queryParameters,!1)};E.prototype.appendQueryParameters=function(e){this._queryParameters=Bt(e,this._queryParameters,!0)};E.prototype.setTemplateValues=function(e,t){t?this._templateValues=qe(this._templateValues,e):this._templateValues=qe(e,this._templateValues)};E.prototype.getDerivedResource=function(e){let t=this.clone();if(t._retryCount=0,d(e.url)){let n=O(e.preserveQueryParameters,!1);t.parseUrl(e.url,!0,n,this._url)}return d(e.queryParameters)&&(t._queryParameters=qe(e.queryParameters,t.queryParameters)),d(e.templateValues)&&(t._templateValues=qe(e.templateValues,t.templateValues)),d(e.headers)&&(t.headers=qe(e.headers,t.headers)),d(e.proxy)&&(t.proxy=e.proxy),d(e.request)&&(t.request=e.request),d(e.retryCallback)&&(t.retryCallback=e.retryCallback),d(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t};E.prototype.retryOnError=function(e){let t=this.retryCallback;if(typeof t!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let n=this;return Promise.resolve(t(this,e)).then(function(i){return++n._retryCount,i})};E.prototype.clone=function(e){return d(e)?(e._url=this._url,e._queryParameters=nt(this._queryParameters),e._templateValues=nt(this._templateValues),e.headers=nt(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new E({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:d(this.credits)?this.credits.slice():void 0})};E.prototype.getBaseUri=function(e){return mr(this.getUrlComponent(e),e)};E.prototype.appendForwardSlash=function(){this._url=fr(this._url)};E.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};E.fetchArrayBuffer=function(e){return new E(e).fetchArrayBuffer()};E.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};E.fetchBlob=function(e){return new E(e).fetchBlob()};E.prototype.fetchImage=function(e){e=O(e,O.EMPTY_OBJECT);let t=O(e.preferImageBitmap,!1),n=O(e.preferBlob,!1),i=O(e.flipY,!1),s=O(e.skipColorSpaceConversion,!1);if(Tn(this.request),!qr||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return Pn({resource:this,flipY:i,skipColorSpaceConversion:s,preferImageBitmap:t});let o=this.fetchBlob();if(!d(o))return;let c,u,p,b;return E.supportsImageBitmapOptions().then(function(g){return c=g,u=c&&t,o}).then(function(g){if(!d(g))return;if(b=g,u)return E.createImageBitmapFromBlob(g,{flipY:i,premultiplyAlpha:!1,skipColorSpaceConversion:s});let y=window.URL.createObjectURL(g);return p=new E({url:y}),Pn({resource:p,flipY:i,skipColorSpaceConversion:s,preferImageBitmap:!1})}).then(function(g){if(d(g))return g.blob=b,u||window.URL.revokeObjectURL(p.url),g}).catch(function(g){return d(p)&&window.URL.revokeObjectURL(p.url),g.blob=b,Promise.reject(g)})};function Pn(e){let t=e.resource,n=e.flipY,i=e.skipColorSpaceConversion,s=e.preferImageBitmap,o=t.request;o.url=t.url,o.requestFunction=function(){let u=!1;!t.isDataUri&&!t.isBlobUri&&(u=t.isCrossOriginUrl);let p=We();return E._Implementations.createImage(o,u,p,n,i,s),p.promise};let c=jt.request(o);if(d(c))return c.catch(function(u){return o.state!==te.FAILED?Promise.reject(u):t.retryOnError(u).then(function(p){return p?(o.state=te.UNISSUED,o.deferred=void 0,Pn({resource:t,flipY:n,skipColorSpaceConversion:i,preferImageBitmap:s})):Promise.reject(u)})})}E.fetchImage=function(e){return new E(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};E.prototype.fetchText=function(){return this.fetch({responseType:"text"})};E.fetchText=function(e){return new E(e).fetchText()};E.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(d(e))return e.then(function(t){if(d(t))return JSON.parse(t)})};E.fetchJson=function(e){return new E(e).fetchJson()};E.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};E.fetchXML=function(e){return new E(e).fetchXML()};E.prototype.fetchJsonp=function(e){e=O(e,"callback"),Tn(this.request);let t;do t=`loadJsonp${J.nextRandomNumber().toString().substring(2,8)}`;while(d(window[t]));return kr(this,e,t)};function kr(e,t,n){let i={};i[t]=n,e.setQueryParameters(i);let s=e.request,o=e.url;s.url=o,s.requestFunction=function(){let u=We();return window[n]=function(p){u.resolve(p);try{delete window[n]}catch{window[n]=void 0}},E._Implementations.loadAndExecuteScript(o,n,u),u.promise};let c=jt.request(s);if(d(c))return c.catch(function(u){return s.state!==te.FAILED?Promise.reject(u):e.retryOnError(u).then(function(p){return p?(s.state=te.UNISSUED,s.deferred=void 0,kr(e,t,n)):Promise.reject(u)})})}E.fetchJsonp=function(e){return new E(e).fetchJsonp(e.callbackParameterName)};E.prototype._makeRequest=function(e){let t=this;Tn(t.request);let n=t.request,i=t.url;n.url=i,n.requestFunction=function(){let o=e.responseType,c=qe(e.headers,t.headers),u=e.overrideMimeType,p=e.method,b=e.data,g=We(),y=E._Implementations.loadWithXhr(i,o,p,b,c,g,u);return d(y)&&d(y.abort)&&(n.cancelFunction=function(){y.abort()}),g.promise};let s=jt.request(n);if(d(s))return s.then(function(o){return n.cancelFunction=void 0,o}).catch(function(o){return n.cancelFunction=void 0,n.state!==te.FAILED?Promise.reject(o):t.retryOnError(o).then(function(c){return c?(n.state=te.UNISSUED,n.deferred=void 0,t.fetch(e)):Promise.reject(o)})})};function Tn(e){if(e.state===te.ISSUED||e.state===te.ACTIVE)throw new Ee("The Resource is already being fetched.");e.state=te.UNISSUED,e.deferred=void 0}var Oi=/^data:(.*?)(;base64)?,(.*)$/;function Nt(e,t){let n=decodeURIComponent(t);return e?atob(n):n}function Ur(e,t){let n=Nt(e,t),i=new ArrayBuffer(n.length),s=new Uint8Array(i);for(let o=0;o<n.length;o++)s[o]=n.charCodeAt(o);return i}function Ei(e,t){t=O(t,"");let n=e[1],i=!!e[2],s=e[3],o,c;switch(t){case"":case"text":return Nt(i,s);case"arraybuffer":return Ur(i,s);case"blob":return o=Ur(i,s),new Blob([o],{type:n});case"document":return c=new DOMParser,c.parseFromString(Nt(i,s),n);case"json":return JSON.parse(Nt(i,s));default:throw new R(`Unhandled responseType: ${t}`)}}E.prototype.fetch=function(e){return e=Re(e,{}),e.method="GET",this._makeRequest(e)};E.fetch=function(e){return new E(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.delete=function(e){return e=Re(e,{}),e.method="DELETE",this._makeRequest(e)};E.delete=function(e){return new E(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};E.prototype.head=function(e){return e=Re(e,{}),e.method="HEAD",this._makeRequest(e)};E.head=function(e){return new E(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.options=function(e){return e=Re(e,{}),e.method="OPTIONS",this._makeRequest(e)};E.options=function(e){return new E(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.post=function(e,t){return w.defined("data",e),t=Re(t,{}),t.method="POST",t.data=e,this._makeRequest(t)};E.post=function(e){return new E(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.put=function(e,t){return w.defined("data",e),t=Re(t,{}),t.method="PUT",t.data=e,this._makeRequest(t)};E.put=function(e){return new E(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E.prototype.patch=function(e,t){return w.defined("data",e),t=Re(t,{}),t.method="PATCH",t.data=e,this._makeRequest(t)};E.patch=function(e){return new E(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};E._Implementations={};E._Implementations.loadImageElement=function(e,t,n){let i=new Image;i.onload=function(){i.naturalWidth===0&&i.naturalHeight===0&&i.width===0&&i.height===0&&(i.width=300,i.height=150),n.resolve(i)},i.onerror=function(s){n.reject(s)},t&&(Rn.contains(e)?i.crossOrigin="use-credentials":i.crossOrigin=""),i.src=e};E._Implementations.createImage=function(e,t,n,i,s,o){let c=e.url;E.supportsImageBitmapOptions().then(function(u){if(!(u&&o)){E._Implementations.loadImageElement(c,t,n);return}let p="blob",b="GET",g=We(),y=E._Implementations.loadWithXhr(c,p,b,void 0,void 0,g,void 0,void 0,void 0);return d(y)&&d(y.abort)&&(e.cancelFunction=function(){y.abort()}),g.promise.then(function(S){if(!d(S)){n.reject(new Ee(`Successfully retrieved ${c} but it contained no content.`));return}return E.createImageBitmapFromBlob(S,{flipY:i,premultiplyAlpha:!1,skipColorSpaceConversion:s})}).then(function(S){n.resolve(S)})}).catch(function(u){n.reject(u)})};E.createImageBitmapFromBlob=function(e,t){return w.defined("options",t),w.typeOf.bool("options.flipY",t.flipY),w.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),w.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};function Dr(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}function Ci(e,t,n,i,s,o,c){let u,p;Promise.all([import("url"),import("zlib")]).then(([b,g])=>(u=b.parse(e),p=g,u.protocol==="https:"?import("https"):import("http"))).then(b=>{let g={protocol:u.protocol,hostname:u.hostname,port:u.port,path:u.path,query:u.query,method:n,headers:s};b.request(g).on("response",function(y){if(y.statusCode<200||y.statusCode>=300){o.reject(new lt(y.statusCode,y,y.headers));return}let S=[];y.on("data",function(A){S.push(A)}),y.on("end",function(){let A=Buffer.concat(S);y.headers["content-encoding"]==="gzip"?p.gunzip(A,function(I,T){I?o.reject(new Ee("Error decompressing response.")):o.resolve(Dr(T,t))}):o.resolve(Dr(A,t))})}).on("error",function(y){o.reject(new lt)}).end()})}var Ai=typeof XMLHttpRequest>"u";E._Implementations.loadWithXhr=function(e,t,n,i,s,o,c){let u=Oi.exec(e);if(u!==null){o.resolve(Ei(u,t));return}if(Ai){Ci(e,t,n,i,s,o,c);return}let p=new XMLHttpRequest;if(Rn.contains(e)&&(p.withCredentials=!0),p.open(n,e,!0),d(c)&&d(p.overrideMimeType)&&p.overrideMimeType(c),d(s))for(let g in s)s.hasOwnProperty(g)&&p.setRequestHeader(g,s[g]);d(t)&&(p.responseType=t);let b=!1;return typeof e=="string"&&(b=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),p.onload=function(){if((p.status<200||p.status>=300)&&!(b&&p.status===0)){o.reject(new lt(p.status,p.response,p.getAllResponseHeaders()));return}let g=p.response,y=p.responseType;if(n==="HEAD"||n==="OPTIONS"){let A=p.getAllResponseHeaders().trim().split(/[\r\n]+/),I={};A.forEach(function(T){let k=T.split(": "),D=k.shift();I[D]=k.join(": ")}),o.resolve(I);return}if(p.status===204)o.resolve();else if(d(g)&&(!d(t)||y===t))o.resolve(g);else if(t==="json"&&typeof g=="string")try{o.resolve(JSON.parse(g))}catch(S){o.reject(S)}else(y===""||y==="document")&&d(p.responseXML)&&p.responseXML.hasChildNodes()?o.resolve(p.responseXML):(y===""||y==="text")&&d(p.responseText)?o.resolve(p.responseText):o.reject(new Ee("Invalid XMLHttpRequest response type."))},p.onerror=function(g){o.reject(new lt)},p.send(i),p};E._Implementations.loadAndExecuteScript=function(e,t,n){return br(e,t).catch(function(i){n.reject(i)})};E._DefaultImplementations={};E._DefaultImplementations.createImage=E._Implementations.createImage;E._DefaultImplementations.loadWithXhr=E._Implementations.loadWithXhr;E._DefaultImplementations.loadAndExecuteScript=E._Implementations.loadAndExecuteScript;E.DEFAULT=Object.freeze(new E({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var Fe=E;function ht(e){e=O(e,O.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=O(e.addNewLeapSeconds,!0),d(e.data)?Fr(this,e.data):Fr(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}ht.fromUrl=async function(e,t){w.defined("url",e),t=O(t,O.EMPTY_OBJECT);let n=Fe.createIfNeeded(e),i;try{i=await n.fetchJson()}catch{throw new Ee(`An error occurred while retrieving the EOP data from the URL ${n.url}.`)}return new ht({addNewLeapSeconds:t.addNewLeapSeconds,data:i})};ht.NONE=Object.freeze({compute:function(e,t){return d(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new ut(0,0,0,0,0),t}});ht.prototype.compute=function(e,t){if(!d(this._samples))return;if(d(t)||(t=new ut(0,0,0,0,0)),this._samples.length===0)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;let n=this._dates,i=this._lastIndex,s=0,o=0;if(d(i)){let u=n[i],p=n[i+1],b=ue.lessThanOrEquals(u,e),g=!d(p),y=g||ue.greaterThanOrEquals(p,e);if(b&&y)return s=i,!g&&p.equals(e)&&++s,o=s+1,jr(this,n,this._samples,e,s,o,t),t}let c=Qe(n,e,ue.compare,this._dateColumn);return c>=0?(c<n.length-1&&n[c+1].equals(e)&&++c,s=c,o=c):(o=~c,s=o-1,s<0&&(s=0)),this._lastIndex=s,jr(this,n,this._samples,e,s,o,t),t};function Ri(e,t){return ue.compare(e.julianDate,t)}function Fr(e,t){if(!d(t.columnNames))throw new Ee("Error in loaded EOP data: The columnNames property is required.");if(!d(t.samples))throw new Ee("Error in loaded EOP data: The samples property is required.");let n=t.columnNames.indexOf("modifiedJulianDateUtc"),i=t.columnNames.indexOf("xPoleWanderRadians"),s=t.columnNames.indexOf("yPoleWanderRadians"),o=t.columnNames.indexOf("ut1MinusUtcSeconds"),c=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),p=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||i<0||s<0||o<0||c<0||u<0||p<0)throw new Ee("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let b=e._samples=t.samples,g=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=i,e._yPoleWanderRadiansColumn=s,e._ut1MinusUtcSecondsColumn=o,e._xCelestialPoleOffsetRadiansColumn=c,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=p,e._columnCount=t.columnNames.length,e._lastIndex=void 0;let y,S=e._addNewLeapSeconds;for(let A=0,I=b.length;A<I;A+=e._columnCount){let T=b[A+n],k=b[A+p],D=T+K.MODIFIED_JULIAN_DATE_DIFFERENCE,F=new ue(D,k,j.TAI);if(g.push(F),S){if(k!==y&&d(y)){let z=ue.leapSeconds,M=Qe(z,F,Ri);if(M<0){let N=new Z(F,k);z.splice(~M,0,N)}}y=k}}}function Mr(e,t,n,i,s){let o=n*i;s.xPoleWander=t[o+e._xPoleWanderRadiansColumn],s.yPoleWander=t[o+e._yPoleWanderRadiansColumn],s.xPoleOffset=t[o+e._xCelestialPoleOffsetRadiansColumn],s.yPoleOffset=t[o+e._yCelestialPoleOffsetRadiansColumn],s.ut1MinusUtc=t[o+e._ut1MinusUtcSecondsColumn]}function mt(e,t,n){return t+e*(n-t)}function jr(e,t,n,i,s,o,c){let u=e._columnCount;if(o>t.length-1)return c.xPoleWander=0,c.yPoleWander=0,c.xPoleOffset=0,c.yPoleOffset=0,c.ut1MinusUtc=0,c;let p=t[s],b=t[o];if(p.equals(b)||i.equals(p))return Mr(e,n,s,u,c),c;if(i.equals(b))return Mr(e,n,o,u,c),c;let g=ue.secondsDifference(i,p)/ue.secondsDifference(b,p),y=s*u,S=o*u,A=n[y+e._ut1MinusUtcSecondsColumn],I=n[S+e._ut1MinusUtcSecondsColumn],T=I-A;if(T>.5||T<-.5){let k=n[y+e._taiMinusUtcSecondsColumn],D=n[S+e._taiMinusUtcSecondsColumn];k!==D&&(b.equals(i)?A=I:I-=D-k)}return c.xPoleWander=mt(g,n[y+e._xPoleWanderRadiansColumn],n[S+e._xPoleWanderRadiansColumn]),c.yPoleWander=mt(g,n[y+e._yPoleWanderRadiansColumn],n[S+e._yPoleWanderRadiansColumn]),c.xPoleOffset=mt(g,n[y+e._xCelestialPoleOffsetRadiansColumn],n[S+e._xCelestialPoleOffsetRadiansColumn]),c.yPoleOffset=mt(g,n[y+e._yCelestialPoleOffsetRadiansColumn],n[S+e._yCelestialPoleOffsetRadiansColumn]),c.ut1MinusUtc=mt(g,A,I),c}var Nr=ht;function de(e,t,n){this.heading=O(e,0),this.pitch=O(t,0),this.roll=O(n,0)}de.fromQuaternion=function(e,t){if(!d(e))throw new R("quaternion is required");d(t)||(t=new de);let n=2*(e.w*e.y-e.z*e.x),i=1-2*(e.x*e.x+e.y*e.y),s=2*(e.w*e.x+e.y*e.z),o=1-2*(e.y*e.y+e.z*e.z),c=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(c,o),t.roll=Math.atan2(s,i),t.pitch=-J.asinClamped(n),t};de.fromDegrees=function(e,t,n,i){if(!d(e))throw new R("heading is required");if(!d(t))throw new R("pitch is required");if(!d(n))throw new R("roll is required");return d(i)||(i=new de),i.heading=e*J.RADIANS_PER_DEGREE,i.pitch=t*J.RADIANS_PER_DEGREE,i.roll=n*J.RADIANS_PER_DEGREE,i};de.clone=function(e,t){if(d(e))return d(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new de(e.heading,e.pitch,e.roll)};de.equals=function(e,t){return e===t||d(e)&&d(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll};de.equalsEpsilon=function(e,t,n,i){return e===t||d(e)&&d(t)&&J.equalsEpsilon(e.heading,t.heading,n,i)&&J.equalsEpsilon(e.pitch,t.pitch,n,i)&&J.equalsEpsilon(e.roll,t.roll,n,i)};de.prototype.clone=function(e){return de.clone(this,e)};de.prototype.equals=function(e){return de.equals(this,e)};de.prototype.equalsEpsilon=function(e,t,n){return de.equalsEpsilon(this,e,t,n)};de.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var In=de;var Br=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function Pi(){let e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){let i=e[t].getAttribute("src"),s=Br.exec(i);if(s!==null)return s[1]}}var Lt;function Lr(e){return typeof document>"u"?e:(d(Lt)||(Lt=document.createElement("a")),Lt.href=e,Lt.href)}var Ye;function Qr(){if(d(Ye))return Ye;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:d(import.meta?.url)?e=ft(".",import.meta.url):typeof define=="object"&&d(define.amd)&&!define.amd.toUrlUndefined&&d(_t.toUrl)?e=ft("..",Ze("Core/buildModuleUrl.js")):e=Pi(),!d(e))throw new R("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Ye=new Fe({url:Lr(e)}),Ye.appendForwardSlash(),Ye}function Ti(e){return Lr(_t.toUrl(`../${e}`))}function $r(e){return Qr().getDerivedResource({url:e}).url}var Qt;function Ze(e){return d(Qt)||(typeof define=="object"&&d(define.amd)&&!define.amd.toUrlUndefined&&d(_t.toUrl)?Qt=Ti:Qt=$r),Qt(e)}Ze._cesiumScriptRegex=Br;Ze._buildModuleUrlFromBaseUrl=$r;Ze._clearBaseResource=function(){Ye=void 0};Ze.setBaseUrl=function(e){Ye=Fe.DEFAULT.getDerivedResource({url:e})};Ze.getCesiumBaseUrl=Qr;var Wr=Ze;function Ii(e,t,n){this.x=e,this.y=t,this.s=n}var $t=Ii;function zn(e){e=O(e,O.EMPTY_OBJECT),this._xysFileUrlTemplate=Fe.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=O(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=O(e.sampleZeroJulianEphemerisDate,24423965e-1),this._sampleZeroDateTT=new ue(this._sampleZeroJulianEphemerisDate,0,j.TAI),this._stepSizeDays=O(e.stepSizeDays,1),this._samplesPerXysFile=O(e.samplesPerXysFile,1e3),this._totalSamples=O(e.totalSamples,27426),this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let t=this._interpolationOrder,n=this._denominators=new Array(t+1),i=this._xTable=new Array(t+1),s=Math.pow(this._stepSizeDays,t);for(let o=0;o<=t;++o){n[o]=s,i[o]=o*this._stepSizeDays;for(let c=0;c<=t;++c)c!==o&&(n[o]*=o-c);n[o]=1/n[o]}this._work=new Array(t+1),this._coef=new Array(t+1)}var Ui=new ue(0,0,j.TAI);function Un(e,t,n){let i=Ui;return i.dayNumber=t,i.secondsOfDay=n,ue.daysDifference(i,e._sampleZeroDateTT)}zn.prototype.preload=function(e,t,n,i){let s=Un(this,e,t),o=Un(this,n,i),c=s/this._stepSizeDays-this._interpolationOrder/2|0;c<0&&(c=0);let u=o/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let p=c/this._samplesPerXysFile|0,b=u/this._samplesPerXysFile|0,g=[];for(let y=p;y<=b;++y)g.push(Dn(this,y));return Promise.all(g)};zn.prototype.computeXysRadians=function(e,t,n){let i=Un(this,e,t);if(i<0)return;let s=i/this._stepSizeDays|0;if(s>=this._totalSamples)return;let o=this._interpolationOrder,c=s-(o/2|0);c<0&&(c=0);let u=c+o;u>=this._totalSamples&&(u=this._totalSamples-1,c=u-o,c<0&&(c=0));let p=!1,b=this._samples;if(d(b[c*3])||(Dn(this,c/this._samplesPerXysFile|0),p=!0),d(b[u*3])||(Dn(this,u/this._samplesPerXysFile|0),p=!0),p)return;d(n)?(n.x=0,n.y=0,n.s=0):n=new $t(0,0,0);let g=i-c*this._stepSizeDays,y=this._work,S=this._denominators,A=this._coef,I=this._xTable,T,k;for(T=0;T<=o;++T)y[T]=g-I[T];for(T=0;T<=o;++T){for(A[T]=1,k=0;k<=o;++k)k!==T&&(A[T]*=y[k]);A[T]*=S[T];let D=(c+T)*3;n.x+=A[T]*b[D++],n.y+=A[T]*b[D++],n.s+=A[T]*b[D]}return n};function Dn(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n,i=e._xysFileUrlTemplate;d(i)?n=i.getDerivedResource({templateValues:{0:t}}):n=new Fe({url:Wr(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});let s=n.fetchJson().then(function(o){e._chunkDownloadsInProgress[t]=!1;let c=e._samples,u=o.samples,p=t*e._samplesPerXysFile*3;for(let b=0,g=u.length;b<g;++b)c[p+b]=u[b]});return e._chunkDownloadsInProgress[t]=s,s}var Vr=zn;var Me,re={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},ge={};Object.defineProperties(ge,{element:{get:function(){if(ge.supportsFullscreen())return document[re.fullscreenElement]}},changeEventName:{get:function(){if(ge.supportsFullscreen())return re.fullscreenchange}},errorEventName:{get:function(){if(ge.supportsFullscreen())return re.fullscreenerror}},enabled:{get:function(){if(ge.supportsFullscreen())return document[re.fullscreenEnabled]}},fullscreen:{get:function(){if(ge.supportsFullscreen())return ge.element!==null}}});ge.supportsFullscreen=function(){if(d(Me))return Me;Me=!1;let e=document.body;if(typeof e.requestFullscreen=="function")return re.requestFullscreen="requestFullscreen",re.exitFullscreen="exitFullscreen",re.fullscreenEnabled="fullscreenEnabled",re.fullscreenElement="fullscreenElement",re.fullscreenchange="fullscreenchange",re.fullscreenerror="fullscreenerror",Me=!0,Me;let t=["webkit","moz","o","ms","khtml"],n;for(let i=0,s=t.length;i<s;++i){let o=t[i];n=`${o}RequestFullscreen`,typeof e[n]=="function"?(re.requestFullscreen=n,Me=!0):(n=`${o}RequestFullScreen`,typeof e[n]=="function"&&(re.requestFullscreen=n,Me=!0)),n=`${o}ExitFullscreen`,typeof document[n]=="function"?re.exitFullscreen=n:(n=`${o}CancelFullScreen`,typeof document[n]=="function"&&(re.exitFullscreen=n)),n=`${o}FullscreenEnabled`,document[n]!==void 0?re.fullscreenEnabled=n:(n=`${o}FullScreenEnabled`,document[n]!==void 0&&(re.fullscreenEnabled=n)),n=`${o}FullscreenElement`,document[n]!==void 0?re.fullscreenElement=n:(n=`${o}FullScreenElement`,document[n]!==void 0&&(re.fullscreenElement=n)),n=`${o}fullscreenchange`,document[`on${n}`]!==void 0&&(o==="ms"&&(n="MSFullscreenChange"),re.fullscreenchange=n),n=`${o}fullscreenerror`,document[`on${n}`]!==void 0&&(o==="ms"&&(n="MSFullscreenError"),re.fullscreenerror=n)}return Me};ge.requestFullscreen=function(e,t){ge.supportsFullscreen()&&e[re.requestFullscreen]({vrDisplay:t})};ge.exitFullscreen=function(){ge.supportsFullscreen()&&document[re.exitFullscreen]()};ge._names=re;var Hr=ge;var me;typeof navigator<"u"?me=navigator:me={};function Je(e){let t=e.split(".");for(let n=0,i=t.length;n<i;++n)t[n]=parseInt(t[n],10);return t}var Wt,Yr;function Nn(){if(!d(Wt)&&(Wt=!1,!Xt())){let e=/ Chrome\/([\.0-9]+)/.exec(me.userAgent);e!==null&&(Wt=!0,Yr=Je(e[1]))}return Wt}function Di(){return Nn()&&Yr}var Vt,Zr;function Jr(){if(!d(Vt)&&(Vt=!1,!Nn()&&!Xt()&&/ Safari\/[\.0-9]+/.test(me.userAgent))){let e=/ Version\/([\.0-9]+)/.exec(me.userAgent);e!==null&&(Vt=!0,Zr=Je(e[1]))}return Vt}function zi(){return Jr()&&Zr}var Ht,Mn;function Xr(){if(!d(Ht)){Ht=!1;let e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(me.userAgent);e!==null&&(Ht=!0,Mn=Je(e[1]),Mn.isNightly=!!e[2])}return Ht}function qi(){return Xr()&&Mn}var yt,jn;function Gr(){if(!d(yt)){yt=!1;let e;me.appName==="Microsoft Internet Explorer"?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(me.userAgent),e!==null&&(yt=!0,jn=Je(e[1]))):me.appName==="Netscape"&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(me.userAgent),e!==null&&(yt=!0,jn=Je(e[1])))}return yt}function ki(){return Gr()&&jn}var Yt,Kr;function Xt(){if(!d(Yt)){Yt=!1;let e=/ Edg\/([\.0-9]+)/.exec(me.userAgent);e!==null&&(Yt=!0,Kr=Je(e[1]))}return Yt}function Fi(){return Xt()&&Kr}var Zt,Bn;function Gt(){if(!d(Zt)){Zt=!1;let e=/Firefox\/([\.0-9]+)/.exec(me.userAgent);e!==null&&(Zt=!0,Bn=Je(e[1]))}return Zt}var qn;function Mi(){return d(qn)||(qn=/Windows/i.test(me.appVersion)),qn}var kn;function ji(){return d(kn)||(kn=navigator.platform==="iPhone"||navigator.platform==="iPod"||navigator.platform==="iPad"),kn}function Ni(){return Gt()&&Bn}var Fn;function Bi(){return d(Fn)||(Fn=!Gt()&&typeof PointerEvent<"u"&&(!d(me.pointerEnabled)||me.pointerEnabled)),Fn}var eo,Jt;function to(){if(!d(Jt)){let e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");let t=e.style.imageRendering;Jt=d(t)&&t!=="",Jt&&(eo=t)}return Jt}function Li(){return to()?eo:void 0}function pe(){if(!pe.initialized)throw new R("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return pe._result}pe._promise=void 0;pe._result=void 0;pe.initialize=function(){return d(pe._promise)||(pe._promise=new Promise(e=>{let t=new Image;t.onload=function(){pe._result=t.width>0&&t.height>0,e(pe._result)},t.onerror=function(){pe._result=!1,e(pe._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"})),pe._promise};Object.defineProperties(pe,{initialized:{get:function(){return d(pe._result)}}});var st=[];typeof ArrayBuffer<"u"&&(st.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),typeof Uint8ClampedArray<"u"&&st.push(Uint8ClampedArray),typeof Uint8ClampedArray<"u"&&st.push(Uint8ClampedArray),typeof BigInt64Array<"u"&&st.push(BigInt64Array),typeof BigUint64Array<"u"&&st.push(BigUint64Array));var Se={isChrome:Nn,chromeVersion:Di,isSafari:Jr,safariVersion:zi,isWebkit:Xr,webkitVersion:qi,isInternetExplorer:Gr,internetExplorerVersion:ki,isEdge:Xt,edgeVersion:Fi,isFirefox:Gt,firefoxVersion:Ni,isWindows:Mi,isIPadOrIOS:ji,hardwareConcurrency:O(me.hardwareConcurrency,3),supportsPointerEvents:Bi,supportsImageRenderingPixelated:to,supportsWebP:pe,imageRenderingValue:Li,typedArrayTypes:st};Se.supportsBasis=function(e){return Se.supportsWebAssembly()&&e.context.supportsBasis};Se.supportsFullscreen=function(){return Hr.supportsFullscreen()};Se.supportsTypedArrays=function(){return typeof ArrayBuffer<"u"};Se.supportsBigInt64Array=function(){return typeof BigInt64Array<"u"};Se.supportsBigUint64Array=function(){return typeof BigUint64Array<"u"};Se.supportsBigInt=function(){return typeof BigInt<"u"};Se.supportsWebWorkers=function(){return typeof Worker<"u"};Se.supportsWebAssembly=function(){return typeof WebAssembly<"u"};Se.supportsWebgl2=function(e){return w.defined("scene",e),e.context.webgl2};Se.supportsEsmWebWorkers=function(){return!Gt()||parseInt(Bn)>=114};var gt=Se;function x(e,t,n,i){this.x=O(e,0),this.y=O(t,0),this.z=O(n,0),this.w=O(i,0)}var wt=new f;x.fromAxisAngle=function(e,t,n){w.typeOf.object("axis",e),w.typeOf.number("angle",t);let i=t/2,s=Math.sin(i);wt=f.normalize(e,wt);let o=wt.x*s,c=wt.y*s,u=wt.z*s,p=Math.cos(i);return d(n)?(n.x=o,n.y=c,n.z=u,n.w=p,n):new x(o,c,u,p)};var Qi=[1,2,0],$i=new Array(3);x.fromRotationMatrix=function(e,t){w.typeOf.object("matrix",e);let n,i,s,o,c,u=e[B.COLUMN0ROW0],p=e[B.COLUMN1ROW1],b=e[B.COLUMN2ROW2],g=u+p+b;if(g>0)n=Math.sqrt(g+1),c=.5*n,n=.5/n,i=(e[B.COLUMN1ROW2]-e[B.COLUMN2ROW1])*n,s=(e[B.COLUMN2ROW0]-e[B.COLUMN0ROW2])*n,o=(e[B.COLUMN0ROW1]-e[B.COLUMN1ROW0])*n;else{let y=Qi,S=0;p>u&&(S=1),b>u&&b>p&&(S=2);let A=y[S],I=y[A];n=Math.sqrt(e[B.getElementIndex(S,S)]-e[B.getElementIndex(A,A)]-e[B.getElementIndex(I,I)]+1);let T=$i;T[S]=.5*n,n=.5/n,c=(e[B.getElementIndex(I,A)]-e[B.getElementIndex(A,I)])*n,T[A]=(e[B.getElementIndex(A,S)]+e[B.getElementIndex(S,A)])*n,T[I]=(e[B.getElementIndex(I,S)]+e[B.getElementIndex(S,I)])*n,i=-T[0],s=-T[1],o=-T[2]}return d(t)?(t.x=i,t.y=s,t.z=o,t.w=c,t):new x(i,s,o,c)};var no=new x,ro=new x,Ln=new x,oo=new x;x.fromHeadingPitchRoll=function(e,t){return w.typeOf.object("headingPitchRoll",e),oo=x.fromAxisAngle(f.UNIT_X,e.roll,no),Ln=x.fromAxisAngle(f.UNIT_Y,-e.pitch,t),t=x.multiply(Ln,oo,Ln),ro=x.fromAxisAngle(f.UNIT_Z,-e.heading,no),x.multiply(ro,t,t)};var Kt=new f,Qn=new f,Oe=new x,io=new x,en=new x;x.packedLength=4;x.pack=function(e,t,n){return w.typeOf.object("value",e),w.defined("array",t),n=O(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t};x.unpack=function(e,t,n){return w.defined("array",e),t=O(t,0),d(n)||(n=new x),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n};x.packedInterpolationLength=3;x.convertPackedArrayForInterpolation=function(e,t,n,i){x.unpack(e,n*4,en),x.conjugate(en,en);for(let s=0,o=n-t+1;s<o;s++){let c=s*3;x.unpack(e,(t+s)*4,Oe),x.multiply(Oe,en,Oe),Oe.w<0&&x.negate(Oe,Oe),x.computeAxis(Oe,Kt);let u=x.computeAngle(Oe);d(i)||(i=[]),i[c]=Kt.x*u,i[c+1]=Kt.y*u,i[c+2]=Kt.z*u}};x.unpackInterpolationResult=function(e,t,n,i,s){d(s)||(s=new x),f.fromArray(e,0,Qn);let o=f.magnitude(Qn);return x.unpack(t,i*4,io),o===0?x.clone(x.IDENTITY,Oe):x.fromAxisAngle(Qn,o,Oe),x.multiply(Oe,io,s)};x.clone=function(e,t){if(d(e))return d(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new x(e.x,e.y,e.z,e.w)};x.conjugate=function(e,t){return w.typeOf.object("quaternion",e),w.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t};x.magnitudeSquared=function(e){return w.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};x.magnitude=function(e){return Math.sqrt(x.magnitudeSquared(e))};x.normalize=function(e,t){w.typeOf.object("result",t);let n=1/x.magnitude(e),i=e.x*n,s=e.y*n,o=e.z*n,c=e.w*n;return t.x=i,t.y=s,t.z=o,t.w=c,t};x.inverse=function(e,t){w.typeOf.object("result",t);let n=x.magnitudeSquared(e);return t=x.conjugate(e,t),x.multiplyByScalar(t,1/n,t)};x.add=function(e,t,n){return w.typeOf.object("left",e),w.typeOf.object("right",t),w.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n};x.subtract=function(e,t,n){return w.typeOf.object("left",e),w.typeOf.object("right",t),w.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n};x.negate=function(e,t){return w.typeOf.object("quaternion",e),w.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t};x.dot=function(e,t){return w.typeOf.object("left",e),w.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w};x.multiply=function(e,t,n){w.typeOf.object("left",e),w.typeOf.object("right",t),w.typeOf.object("result",n);let i=e.x,s=e.y,o=e.z,c=e.w,u=t.x,p=t.y,b=t.z,g=t.w,y=c*u+i*g+s*b-o*p,S=c*p-i*b+s*g+o*u,A=c*b+i*p-s*u+o*g,I=c*g-i*u-s*p-o*b;return n.x=y,n.y=S,n.z=A,n.w=I,n};x.multiplyByScalar=function(e,t,n){return w.typeOf.object("quaternion",e),w.typeOf.number("scalar",t),w.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n};x.divideByScalar=function(e,t,n){return w.typeOf.object("quaternion",e),w.typeOf.number("scalar",t),w.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n};x.computeAxis=function(e,t){w.typeOf.object("quaternion",e),w.typeOf.object("result",t);let n=e.w;if(Math.abs(n-1)<J.EPSILON6||Math.abs(n+1)<J.EPSILON6)return t.x=1,t.y=t.z=0,t;let i=1/Math.sqrt(1-n*n);return t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t};x.computeAngle=function(e){return w.typeOf.object("quaternion",e),Math.abs(e.w-1)<J.EPSILON6?0:2*Math.acos(e.w)};var $n=new x;x.lerp=function(e,t,n,i){return w.typeOf.object("start",e),w.typeOf.object("end",t),w.typeOf.number("t",n),w.typeOf.object("result",i),$n=x.multiplyByScalar(t,n,$n),i=x.multiplyByScalar(e,1-n,i),x.add($n,i,i)};var so=new x,Wn=new x,Vn=new x;x.slerp=function(e,t,n,i){w.typeOf.object("start",e),w.typeOf.object("end",t),w.typeOf.number("t",n),w.typeOf.object("result",i);let s=x.dot(e,t),o=t;if(s<0&&(s=-s,o=so=x.negate(t,so)),1-s<J.EPSILON6)return x.lerp(e,o,n,i);let c=Math.acos(s);return Wn=x.multiplyByScalar(e,Math.sin((1-n)*c),Wn),Vn=x.multiplyByScalar(o,Math.sin(n*c),Vn),i=x.add(Wn,Vn,i),x.multiplyByScalar(i,1/Math.sin(c),i)};x.log=function(e,t){w.typeOf.object("quaternion",e),w.typeOf.object("result",t);let n=J.acosClamped(e.w),i=0;return n!==0&&(i=n/Math.sin(n)),f.multiplyByScalar(e,i,t)};x.exp=function(e,t){w.typeOf.object("cartesian",e),w.typeOf.object("result",t);let n=f.magnitude(e),i=0;return n!==0&&(i=Math.sin(n)/n),t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t.w=Math.cos(n),t};var Wi=new f,Vi=new f,bt=new x,at=new x;x.computeInnerQuadrangle=function(e,t,n,i){w.typeOf.object("q0",e),w.typeOf.object("q1",t),w.typeOf.object("q2",n),w.typeOf.object("result",i);let s=x.conjugate(t,bt);x.multiply(s,n,at);let o=x.log(at,Wi);x.multiply(s,e,at);let c=x.log(at,Vi);return f.add(o,c,o),f.multiplyByScalar(o,.25,o),f.negate(o,o),x.exp(o,bt),x.multiply(t,bt,i)};x.squad=function(e,t,n,i,s,o){w.typeOf.object("q0",e),w.typeOf.object("q1",t),w.typeOf.object("s0",n),w.typeOf.object("s1",i),w.typeOf.number("t",s),w.typeOf.object("result",o);let c=x.slerp(e,t,s,bt),u=x.slerp(n,i,s,at);return x.slerp(c,u,2*s*(1-s),o)};var Hi=new x,ao=1.9011074535173003,tn=gt.supportsTypedArrays()?new Float32Array(8):[],nn=gt.supportsTypedArrays()?new Float32Array(8):[],Te=gt.supportsTypedArrays()?new Float32Array(8):[],Ie=gt.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let t=e+1,n=2*t+1;tn[e]=1/(t*n),nn[e]=t/n}tn[7]=ao/(8*17);nn[7]=ao*8/17;x.fastSlerp=function(e,t,n,i){w.typeOf.object("start",e),w.typeOf.object("end",t),w.typeOf.number("t",n),w.typeOf.object("result",i);let s=x.dot(e,t),o;s>=0?o=1:(o=-1,s=-s);let c=s-1,u=1-n,p=n*n,b=u*u;for(let A=7;A>=0;--A)Te[A]=(tn[A]*p-nn[A])*c,Ie[A]=(tn[A]*b-nn[A])*c;let g=o*n*(1+Te[0]*(1+Te[1]*(1+Te[2]*(1+Te[3]*(1+Te[4]*(1+Te[5]*(1+Te[6]*(1+Te[7])))))))),y=u*(1+Ie[0]*(1+Ie[1]*(1+Ie[2]*(1+Ie[3]*(1+Ie[4]*(1+Ie[5]*(1+Ie[6]*(1+Ie[7])))))))),S=x.multiplyByScalar(e,y,Hi);return x.multiplyByScalar(t,g,i),x.add(S,i,i)};x.fastSquad=function(e,t,n,i,s,o){w.typeOf.object("q0",e),w.typeOf.object("q1",t),w.typeOf.object("s0",n),w.typeOf.object("s1",i),w.typeOf.number("t",s),w.typeOf.object("result",o);let c=x.fastSlerp(e,t,s,bt),u=x.fastSlerp(n,i,s,at);return x.fastSlerp(c,u,2*s*(1-s),o)};x.equals=function(e,t){return e===t||d(e)&&d(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w};x.equalsEpsilon=function(e,t,n){return n=O(n,0),e===t||d(e)&&d(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n};x.ZERO=Object.freeze(new x(0,0,0,0));x.IDENTITY=Object.freeze(new x(0,0,0,1));x.prototype.clone=function(e){return x.clone(this,e)};x.prototype.equals=function(e){return x.equals(this,e)};x.prototype.equalsEpsilon=function(e,t){return x.equalsEpsilon(this,e,t)};x.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xe=x;var $={},Hn={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},ct={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Yn={},we={east:new f,north:new f,up:new f,west:new f,south:new f,down:new f},je=new f,Ne=new f,Be=new f;$.localFrameToFixedFrameGenerator=function(e,t){if(!Hn.hasOwnProperty(e)||!Hn[e].hasOwnProperty(t))throw new R("firstAxis and secondAxis must be east, north, up, west, south or down.");let n=Hn[e][t],i,s=e+t;return d(Yn[s])?i=Yn[s]:(i=function(o,c,u){if(!d(o))throw new R("origin is required.");if(d(u)||(u=new L),f.equalsEpsilon(o,f.ZERO,J.EPSILON14))f.unpack(ct[e],0,je),f.unpack(ct[t],0,Ne),f.unpack(ct[n],0,Be);else if(J.equalsEpsilon(o.x,0,J.EPSILON14)&&J.equalsEpsilon(o.y,0,J.EPSILON14)){let p=J.sign(o.z);f.unpack(ct[e],0,je),e!=="east"&&e!=="west"&&f.multiplyByScalar(je,p,je),f.unpack(ct[t],0,Ne),t!=="east"&&t!=="west"&&f.multiplyByScalar(Ne,p,Ne),f.unpack(ct[n],0,Be),n!=="east"&&n!=="west"&&f.multiplyByScalar(Be,p,Be)}else{c=O(c,ze.WGS84),c.geodeticSurfaceNormal(o,we.up);let p=we.up,b=we.east;b.x=-o.y,b.y=o.x,b.z=0,f.normalize(b,we.east),f.cross(p,b,we.north),f.multiplyByScalar(we.up,-1,we.down),f.multiplyByScalar(we.east,-1,we.west),f.multiplyByScalar(we.north,-1,we.south),je=we[e],Ne=we[t],Be=we[n]}return u[0]=je.x,u[1]=je.y,u[2]=je.z,u[3]=0,u[4]=Ne.x,u[5]=Ne.y,u[6]=Ne.z,u[7]=0,u[8]=Be.x,u[9]=Be.y,u[10]=Be.z,u[11]=0,u[12]=o.x,u[13]=o.y,u[14]=o.z,u[15]=1,u},Yn[s]=i),i};$.eastNorthUpToFixedFrame=$.localFrameToFixedFrameGenerator("east","north");$.northEastDownToFixedFrame=$.localFrameToFixedFrameGenerator("north","east");$.northUpEastToFixedFrame=$.localFrameToFixedFrameGenerator("north","up");$.northWestUpToFixedFrame=$.localFrameToFixedFrameGenerator("north","west");var Yi=new Xe,Zi=new f(1,1,1),Ji=new L;$.headingPitchRollToFixedFrame=function(e,t,n,i,s){w.typeOf.object("HeadingPitchRoll",t),i=O(i,$.eastNorthUpToFixedFrame);let o=Xe.fromHeadingPitchRoll(t,Yi),c=L.fromTranslationQuaternionRotationScale(f.ZERO,o,Zi,Ji);return s=i(e,n,s),L.multiply(s,c,s)};var Xi=new L,Gi=new B;$.headingPitchRollQuaternion=function(e,t,n,i,s){w.typeOf.object("HeadingPitchRoll",t);let o=$.headingPitchRollToFixedFrame(e,t,n,i,Xi),c=L.getMatrix3(o,Gi);return Xe.fromRotationMatrix(c,s)};var Ki=new f(1,1,1),es=new f,co=new L,ts=new L,ns=new B,rs=new Xe;$.fixedFrameToHeadingPitchRoll=function(e,t,n,i){w.defined("transform",e),t=O(t,ze.WGS84),n=O(n,$.eastNorthUpToFixedFrame),d(i)||(i=new In);let s=L.getTranslation(e,es);if(f.equals(s,f.ZERO))return i.heading=0,i.pitch=0,i.roll=0,i;let o=L.inverseTransformation(n(s,t,co),co),c=L.setScale(e,Ki,ts);c=L.setTranslation(c,f.ZERO,c),o=L.multiply(o,c,o);let u=Xe.fromRotationMatrix(L.getMatrix3(o,ns),rs);return u=Xe.normalize(u,u),In.fromQuaternion(u,i)};var os=6*3600+41*60+50.54841,is=8640184812866e-6,ss=.093104,as=-62e-7,cs=11772758384668e-32,us=72921158553e-15,fs=J.TWO_PI/86400,rn=new ue;$.computeTemeToPseudoFixedMatrix=function(e,t){if(!d(e))throw new R("date is required.");rn=ue.addSeconds(e,-ue.computeTaiMinusUtc(e),rn);let n=rn.dayNumber,i=rn.secondsOfDay,s,o=n-2451545;i>=43200?s=(o+.5)/K.DAYS_PER_JULIAN_CENTURY:s=(o-.5)/K.DAYS_PER_JULIAN_CENTURY;let u=(os+s*(is+s*(ss+s*as)))*fs%J.TWO_PI,p=us+cs*(n-24515455e-1),b=(i+K.SECONDS_PER_DAY*.5)%K.SECONDS_PER_DAY,g=u+p*b,y=Math.cos(g),S=Math.sin(g);return d(t)?(t[0]=y,t[1]=-S,t[2]=0,t[3]=S,t[4]=y,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new B(y,S,0,-S,y,0,0,0,1)};$.iau2006XysData=new Vr;$.earthOrientationParameters=Nr.NONE;var Xn=32.184,ls=2451545;$.preloadIcrfFixed=function(e){let t=e.start.dayNumber,n=e.start.secondsOfDay+Xn,i=e.stop.dayNumber,s=e.stop.secondsOfDay+Xn;return $.iau2006XysData.preload(t,n,i,s)};$.computeIcrfToFixedMatrix=function(e,t){if(!d(e))throw new R("date is required.");d(t)||(t=new B);let n=$.computeFixedToIcrfMatrix(e,t);if(d(n))return B.transpose(n,t)};var ds=new $t(0,0,0),ps=new ut(0,0,0,0,0,0),Zn=new B,Jn=new B;$.computeFixedToIcrfMatrix=function(e,t){if(!d(e))throw new R("date is required.");d(t)||(t=new B);let n=$.earthOrientationParameters.compute(e,ps);if(!d(n))return;let i=e.dayNumber,s=e.secondsOfDay+Xn,o=$.iau2006XysData.computeXysRadians(i,s,ds);if(!d(o))return;let c=o.x+n.xPoleOffset,u=o.y+n.yPoleOffset,p=1/(1+Math.sqrt(1-c*c-u*u)),b=Zn;b[0]=1-p*c*c,b[3]=-p*c*u,b[6]=c,b[1]=-p*c*u,b[4]=1-p*u*u,b[7]=u,b[2]=-c,b[5]=-u,b[8]=1-p*(c*c+u*u);let g=B.fromRotationZ(-o.s,Jn),y=B.multiply(b,g,Zn),S=e.dayNumber,A=e.secondsOfDay-ue.computeTaiMinusUtc(e)+n.ut1MinusUtc,I=S-2451545,T=A/K.SECONDS_PER_DAY,k=.779057273264+T+.00273781191135448*(I+T);k=k%1*J.TWO_PI;let D=B.fromRotationZ(k,Jn),F=B.multiply(y,D,Zn),z=Math.cos(n.xPoleWander),M=Math.cos(n.yPoleWander),N=Math.sin(n.xPoleWander),Y=Math.sin(n.yPoleWander),oe=i-ls+s/K.SECONDS_PER_DAY;oe/=36525;let ie=-47e-6*oe*J.RADIANS_PER_DEGREE/3600,X=Math.cos(ie),W=Math.sin(ie),Q=Jn;return Q[0]=z*X,Q[1]=z*W,Q[2]=N,Q[3]=-M*W+Y*N*X,Q[4]=M*X+Y*N*W,Q[5]=-Y*z,Q[6]=-Y*W-M*N*X,Q[7]=Y*X-M*N*W,Q[8]=M*z,B.multiply(F,Q,t)};var ms=new xt;$.pointToWindowCoordinates=function(e,t,n,i){return i=$.pointToGLWindowCoordinates(e,t,n,i),i.y=2*t[5]-i.y,i};$.pointToGLWindowCoordinates=function(e,t,n,i){if(!d(e))throw new R("modelViewProjectionMatrix is required.");if(!d(t))throw new R("viewportTransformation is required.");if(!d(n))throw new R("point is required.");d(i)||(i=new sn);let s=ms;return L.multiplyByVector(e,xt.fromElements(n.x,n.y,n.z,1,s),s),xt.multiplyByScalar(s,1/s.w,s),L.multiplyByVector(t,s,s),sn.fromCartesian4(s,i)};var hs=new f,ys=new f,gs=new f;$.rotationMatrixFromPositionVelocity=function(e,t,n,i){if(!d(e))throw new R("position is required.");if(!d(t))throw new R("velocity is required.");let s=O(n,ze.WGS84).geodeticSurfaceNormal(e,hs),o=f.cross(t,s,ys);f.equalsEpsilon(o,f.ZERO,J.EPSILON6)&&(o=f.clone(f.UNIT_X,o));let c=f.cross(o,t,gs);return f.normalize(c,c),f.cross(t,c,o),f.negate(o,o),f.normalize(o,o),d(i)||(i=new B),i[0]=t.x,i[1]=t.y,i[2]=t.z,i[3]=o.x,i[4]=o.y,i[5]=o.z,i[6]=c.x,i[7]=c.y,i[8]=c.z,i};var uo=new L(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),fo=new De,lo=new f,ws=new f,bs=new B,Gn=new L,po=new L;$.basisTo2D=function(e,t,n){if(!d(e))throw new R("projection is required.");if(!d(t))throw new R("matrix is required.");if(!d(n))throw new R("result is required.");let i=L.getTranslation(t,ws),s=e.ellipsoid,o=s.cartesianToCartographic(i,fo),c=e.project(o,lo);f.fromElements(c.z,c.x,c.y,c);let u=$.eastNorthUpToFixedFrame(i,s,Gn),p=L.inverseTransformation(u,po),b=L.getMatrix3(t,bs),g=L.multiplyByMatrix3(p,b,n);return L.multiply(uo,g,n),L.setTranslation(n,c,n),n};$.wgs84To2DModelMatrix=function(e,t,n){if(!d(e))throw new R("projection is required.");if(!d(t))throw new R("center is required.");if(!d(n))throw new R("result is required.");let i=e.ellipsoid,s=$.eastNorthUpToFixedFrame(t,i,Gn),o=L.inverseTransformation(s,po),c=i.cartesianToCartographic(t,fo),u=e.project(c,lo);f.fromElements(u.z,u.x,u.y,u);let p=L.fromTranslation(u,Gn);return L.multiply(uo,o,n),L.multiply(p,n,n),n};var nf=$;export{an as a,Et as b,Kn as c,Qs as d,gt as e,Xe as f,Fe as g,Wr as h,nf as i};
