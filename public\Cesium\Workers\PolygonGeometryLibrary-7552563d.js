/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.98
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./ArcType-24f44850","./arrayRemoveDuplicates-fd3a3f4e","./Matrix2-7dfd434a","./ComponentDatatype-9b23164a","./defaultValue-50f7432c","./EllipsoidRhumbLine-5454653c","./GeometryAttribute-4d82fade","./GeometryAttributes-8bab1b25","./GeometryPipeline-33e32ecb","./IndexDatatype-ceed713e","./PolygonPipeline-898e8861","./Transforms-f305a473"],(function(e,t,n,i,o,r,a,s,c,l,u,h,p){"use strict";function d(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(d.prototype,{length:{get:function(){return this._length}}}),d.prototype.enqueue=function(e){this._array.push(e),this._length++},d.prototype.dequeue=function(){if(0===this._length)return;const e=this._array;let t=this._offset;const n=e[t];return e[t]=void 0,t++,t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,n},d.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},d.prototype.contains=function(e){return-1!==this._array.indexOf(e)},d.prototype.clear=function(){this._array.length=this._offset=this._length=0},d.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};const f={computeHierarchyPackedLength:function(e,t){let n=0;const i=[e];for(;i.length>0;){const e=i.pop();if(!r.defined(e))continue;n+=2;const o=e.positions,a=e.holes;if(r.defined(o)&&o.length>0&&(n+=o.length*t.packedLength),r.defined(a)){const e=a.length;for(let t=0;t<e;++t)i.push(a[t])}}return n},packPolygonHierarchy:function(e,t,n,i){const o=[e];for(;o.length>0;){const e=o.pop();if(!r.defined(e))continue;const a=e.positions,s=e.holes;if(t[n++]=r.defined(a)?a.length:0,t[n++]=r.defined(s)?s.length:0,r.defined(a)){const e=a.length;for(let o=0;o<e;++o,n+=i.packedLength)i.pack(a[o],t,n)}if(r.defined(s)){const e=s.length;for(let t=0;t<e;++t)o.push(s[t])}}return n},unpackPolygonHierarchy:function(e,t,n){const i=e[t++],o=e[t++],r=new Array(i),a=o>0?new Array(o):void 0;for(let o=0;o<i;++o,t+=n.packedLength)r[o]=n.unpack(e,t);for(let i=0;i<o;++i)a[i]=f.unpackPolygonHierarchy(e,t,n),t=a[i].startingIndex,delete a[i].startingIndex;return{positions:r,holes:a,startingIndex:t}}},y=new i.Cartesian2;function g(e,t,n,o){return i.Cartesian2.subtract(t,e,y),i.Cartesian2.multiplyByScalar(y,n/o,y),i.Cartesian2.add(e,y,y),[y.x,y.y]}const m=new i.Cartesian3;function C(e,t,n,o){return i.Cartesian3.subtract(t,e,m),i.Cartesian3.multiplyByScalar(m,n/o,m),i.Cartesian3.add(e,m,m),[m.x,m.y,m.z]}f.subdivideLineCount=function(e,t,n){const r=i.Cartesian3.distance(e,t)/n,a=Math.max(0,Math.ceil(o.CesiumMath.log2(r)));return Math.pow(2,a)};const b=new i.Cartographic,T=new i.Cartographic,x=new i.Cartographic,v=new i.Cartesian3,w=new a.EllipsoidRhumbLine;f.subdivideRhumbLineCount=function(e,t,n,i){const r=e.cartesianToCartographic(t,b),s=e.cartesianToCartographic(n,T),c=new a.EllipsoidRhumbLine(r,s,e).surfaceDistance/i,l=Math.max(0,Math.ceil(o.CesiumMath.log2(c)));return Math.pow(2,l)},f.subdivideTexcoordLine=function(e,t,n,o,r,a){const s=f.subdivideLineCount(n,o,r),c=i.Cartesian2.distance(e,t),l=c/s,u=a;u.length=2*s;let h=0;for(let n=0;n<s;n++){const i=g(e,t,n*l,c);u[h++]=i[0],u[h++]=i[1]}return u},f.subdivideLine=function(e,t,n,o){const a=f.subdivideLineCount(e,t,n),s=i.Cartesian3.distance(e,t),c=s/a;r.defined(o)||(o=[]);const l=o;l.length=3*a;let u=0;for(let n=0;n<a;n++){const i=C(e,t,n*c,s);l[u++]=i[0],l[u++]=i[1],l[u++]=i[2]}return l},f.subdivideTexcoordRhumbLine=function(e,t,n,r,a,s,c){const l=n.cartesianToCartographic(r,b),u=n.cartesianToCartographic(a,T);w.setEndPoints(l,u);const h=w.surfaceDistance/s,p=Math.max(0,Math.ceil(o.CesiumMath.log2(h))),d=Math.pow(2,p),f=i.Cartesian2.distance(e,t),y=f/d,m=c;m.length=2*d;let C=0;for(let n=0;n<d;n++){const i=g(e,t,n*y,f);m[C++]=i[0],m[C++]=i[1]}return m},f.subdivideRhumbLine=function(e,t,n,i,s){const c=e.cartesianToCartographic(t,b),l=e.cartesianToCartographic(n,T),u=new a.EllipsoidRhumbLine(c,l,e),h=u.surfaceDistance/i,p=Math.max(0,Math.ceil(o.CesiumMath.log2(h))),d=Math.pow(2,p),f=u.surfaceDistance/d;r.defined(s)||(s=[]);const y=s;y.length=3*d;let g=0;for(let t=0;t<d;t++){const n=u.interpolateUsingSurfaceDistance(t*f,x),i=e.cartographicToCartesian(n,v);y[g++]=i.x,y[g++]=i.y,y[g++]=i.z}return y};const A=new i.Cartesian3,L=new i.Cartesian3,E=new i.Cartesian3,I=new i.Cartesian3;f.scaleToGeodeticHeightExtruded=function(e,t,n,o,a){o=r.defaultValue(o,i.Ellipsoid.WGS84);const s=A;let c=L;const l=E;let u=I;if(r.defined(e)&&r.defined(e.attributes)&&r.defined(e.attributes.position)){const r=e.attributes.position.values,h=r.length/2;for(let e=0;e<h;e+=3)i.Cartesian3.fromArray(r,e,l),o.geodeticSurfaceNormal(l,s),u=o.scaleToGeodeticSurface(l,u),c=i.Cartesian3.multiplyByScalar(s,n,c),c=i.Cartesian3.add(u,c,c),r[e+h]=c.x,r[e+1+h]=c.y,r[e+2+h]=c.z,a&&(u=i.Cartesian3.clone(l,u)),c=i.Cartesian3.multiplyByScalar(s,t,c),c=i.Cartesian3.add(u,c,c),r[e]=c.x,r[e+1]=c.y,r[e+2]=c.z}return e},f.polygonOutlinesFromHierarchy=function(e,t,o){const a=[],s=new d;let c,l,u;for(s.enqueue(e);0!==s.length;){const e=s.dequeue();let h=e.positions;if(t)for(u=h.length,c=0;c<u;c++)o.scaleToGeodeticSurface(h[c],h[c]);if(h=n.arrayRemoveDuplicates(h,i.Cartesian3.equalsEpsilon,!0),h.length<3)continue;const p=e.holes?e.holes.length:0;for(c=0;c<p;c++){const h=e.holes[c];let p=h.positions;if(t)for(u=p.length,l=0;l<u;++l)o.scaleToGeodeticSurface(p[l],p[l]);if(p=n.arrayRemoveDuplicates(p,i.Cartesian3.equalsEpsilon,!0),p.length<3)continue;a.push(p);let d=0;for(r.defined(h.holes)&&(d=h.holes.length),l=0;l<d;l++)s.enqueue(h.holes[l])}a.push(h)}return a},f.polygonsFromHierarchy=function(e,t,o,a,s){const c=[],l=[],u=new d;for(u.enqueue(e);0!==u.length;){const e=u.dequeue();let p=e.positions;const d=e.holes;let f,y;if(a)for(y=p.length,f=0;f<y;f++)s.scaleToGeodeticSurface(p[f],p[f]);if(t||(p=n.arrayRemoveDuplicates(p,i.Cartesian3.equalsEpsilon,!0)),p.length<3)continue;let g=o(p);if(!r.defined(g))continue;const m=[];let C=h.PolygonPipeline.computeWindingOrder2D(g);C===h.WindingOrder.CLOCKWISE&&(g.reverse(),p=p.slice().reverse());let b=p.slice();const T=r.defined(d)?d.length:0,x=[];let v;for(f=0;f<T;f++){const e=d[f];let c=e.positions;if(a)for(y=c.length,v=0;v<y;++v)s.scaleToGeodeticSurface(c[v],c[v]);if(t||(c=n.arrayRemoveDuplicates(c,i.Cartesian3.equalsEpsilon,!0)),c.length<3)continue;const l=o(c);if(!r.defined(l))continue;C=h.PolygonPipeline.computeWindingOrder2D(l),C===h.WindingOrder.CLOCKWISE&&(l.reverse(),c=c.slice().reverse()),x.push(c),m.push(b.length),b=b.concat(c),g=g.concat(l);let p=0;for(r.defined(e.holes)&&(p=e.holes.length),v=0;v<p;v++)u.enqueue(e.holes[v])}c.push({outerRing:p,holes:x}),l.push({positions:b,positions2D:g,holes:m})}return{hierarchy:c,polygons:l}};const P=new i.Cartesian2,D=new i.Cartesian3,M=new p.Quaternion,_=new i.Matrix3;f.computeBoundingRectangle=function(e,t,n,o,a){const s=p.Quaternion.fromAxisAngle(e,o,M),c=i.Matrix3.fromQuaternion(s,_);let l=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,h=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;const f=n.length;for(let e=0;e<f;++e){const o=i.Cartesian3.clone(n[e],D);i.Matrix3.multiplyByVector(c,o,o);const a=t(o,P);r.defined(a)&&(l=Math.min(l,a.x),u=Math.max(u,a.x),h=Math.min(h,a.y),d=Math.max(d,a.y))}return a.x=l,a.y=h,a.width=u-l,a.height=d-h,a},f.createGeometryFromPositions=function(e,n,a,c,u,p,d){let f=h.PolygonPipeline.triangulate(n.positions2D,n.holes);f.length<3&&(f=[0,1,2]);const y=n.positions,g=r.defined(a),m=g?a.positions:void 0;if(u){const e=y.length,t=new Array(3*e);let n=0;for(let i=0;i<e;i++){const e=y[i];t[n++]=e.x,t[n++]=e.y,t[n++]=e.z}const r={attributes:{position:new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})},indices:f,primitiveType:s.PrimitiveType.TRIANGLES};g&&(r.attributes.st=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:i.Cartesian2.packArray(m)}));const a=new s.Geometry(r);return p.normal?l.GeometryPipeline.computeNormal(a):a}return d===t.ArcType.GEODESIC?h.PolygonPipeline.computeSubdivision(e,y,f,m,c):d===t.ArcType.RHUMB?h.PolygonPipeline.computeRhumbLineSubdivision(e,y,f,m,c):void 0};const G=[],S=[],R=new i.Cartesian3,N=new i.Cartesian3;f.computeWallGeometry=function(e,n,a,l,h,p){let d,y,g,m,C,b,T,x,v,w=e.length,A=0,L=0;const E=r.defined(n),I=E?n.positions:void 0;if(h)for(y=3*w*2,d=new Array(2*y),E&&(v=2*w*2,x=new Array(2*v)),g=0;g<w;g++)m=e[g],C=e[(g+1)%w],d[A]=d[A+y]=m.x,++A,d[A]=d[A+y]=m.y,++A,d[A]=d[A+y]=m.z,++A,d[A]=d[A+y]=C.x,++A,d[A]=d[A+y]=C.y,++A,d[A]=d[A+y]=C.z,++A,E&&(b=I[g],T=I[(g+1)%w],x[L]=x[L+v]=b.x,++L,x[L]=x[L+v]=b.y,++L,x[L]=x[L+v]=T.x,++L,x[L]=x[L+v]=T.y,++L);else{const n=o.CesiumMath.chordLength(l,a.maximumRadius);let i=0;if(p===t.ArcType.GEODESIC)for(g=0;g<w;g++)i+=f.subdivideLineCount(e[g],e[(g+1)%w],n);else if(p===t.ArcType.RHUMB)for(g=0;g<w;g++)i+=f.subdivideRhumbLineCount(a,e[g],e[(g+1)%w],n);for(y=3*(i+w),d=new Array(2*y),E&&(v=2*(i+w),x=new Array(2*v)),g=0;g<w;g++){let i,o;m=e[g],C=e[(g+1)%w],E&&(b=I[g],T=I[(g+1)%w]),p===t.ArcType.GEODESIC?(i=f.subdivideLine(m,C,n,S),E&&(o=f.subdivideTexcoordLine(b,T,m,C,n,G))):p===t.ArcType.RHUMB&&(i=f.subdivideRhumbLine(a,m,C,n,S),E&&(o=f.subdivideTexcoordRhumbLine(b,T,a,m,C,n,G)));const r=i.length;for(let e=0;e<r;++e,++A)d[A]=i[e],d[A+y]=i[e];if(d[A]=C.x,d[A+y]=C.x,++A,d[A]=C.y,d[A+y]=C.y,++A,d[A]=C.z,d[A+y]=C.z,++A,E){const e=o.length;for(let t=0;t<e;++t,++L)x[L]=o[t],x[L+v]=o[t];x[L]=T.x,x[L+v]=T.x,++L,x[L]=T.y,x[L+v]=T.y,++L}}}w=d.length;const P=u.IndexDatatype.createTypedArray(w/3,w-6*e.length);let D=0;for(w/=6,g=0;g<w;g++){const e=g,t=e+1,n=e+w,r=n+1;m=i.Cartesian3.fromArray(d,3*e,R),C=i.Cartesian3.fromArray(d,3*t,N),i.Cartesian3.equalsEpsilon(m,C,o.CesiumMath.EPSILON10,o.CesiumMath.EPSILON10)||(P[D++]=e,P[D++]=n,P[D++]=t,P[D++]=t,P[D++]=n,P[D++]=r)}const M={attributes:new c.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})}),indices:P,primitiveType:s.PrimitiveType.TRIANGLES};E&&(M.attributes.st=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:x}));return new s.Geometry(M)};var O=f;e.PolygonGeometryLibrary=O}));
